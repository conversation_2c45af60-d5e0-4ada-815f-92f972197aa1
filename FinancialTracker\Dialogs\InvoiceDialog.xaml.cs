using FinancialTracker.Models;
using FinancialTracker.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace FinancialTracker.Dialogs
{
    /// <summary>
    /// منطق التفاعل لنافذة حوار الفاتورة
    /// </summary>
    public partial class InvoiceDialog : Window
    {
        private readonly IDataService _dataService;
        private readonly IFileService _fileService;
        private Invoice? _currentInvoice;
        private bool _isEditMode;
        private List<Project> _projects;
        private List<Commitment> _commitments;

        /// <summary>
        /// الفاتورة الحالية بعد الحفظ
        /// </summary>
        public Invoice? Result { get; private set; }

        /// <summary>
        /// منشئ نافذة إضافة فاتورة جديدة
        /// </summary>
        public InvoiceDialog(IDataService dataService, IFileService fileService)
        {
            InitializeComponent();
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            _fileService = fileService ?? throw new ArgumentNullException(nameof(fileService));
            _isEditMode = false;
            _projects = new List<Project>();
            _commitments = new List<Commitment>();
            
            InitializeAsync();
        }

        /// <summary>
        /// منشئ نافذة تعديل فاتورة موجودة
        /// </summary>
        public InvoiceDialog(IDataService dataService, IFileService fileService, Invoice invoice)
        {
            InitializeComponent();
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            _fileService = fileService ?? throw new ArgumentNullException(nameof(fileService));
            _currentInvoice = invoice ?? throw new ArgumentNullException(nameof(invoice));
            _isEditMode = true;
            _projects = new List<Project>();
            _commitments = new List<Commitment>();
            
            InitializeAsync();
        }

        /// <summary>
        /// تهيئة النافذة وتحميل البيانات
        /// </summary>
        private async void InitializeAsync()
        {
            try
            {
                // تحميل المشاريع والارتباطات
                _projects = await _dataService.GetProjectsAsync();
                _commitments = await _dataService.GetCommitmentsAsync();
                
                CmbProject.ItemsSource = _projects;
                CmbCommitment.ItemsSource = _commitments;

                if (_isEditMode)
                {
                    InitializeForEditInvoice();
                }
                else
                {
                    InitializeForNewInvoice();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات:\n{ex.Message}", 
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تهيئة النافذة لإضافة فاتورة جديدة
        /// </summary>
        private void InitializeForNewInvoice()
        {
            TxtDialogTitle.Text = "إضافة فاتورة جديدة";
            DpIssueDate.SelectedDate = DateTime.Now;
            CmbInvoiceType.SelectedIndex = 0; // Task
            CmbInvoiceStatus.SelectedIndex = 0; // Pending
            ExpanderAdditionalInfo.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// تهيئة النافذة لتعديل فاتورة موجودة
        /// </summary>
        private void InitializeForEditInvoice()
        {
            if (_currentInvoice == null) return;

            TxtDialogTitle.Text = "تعديل الفاتورة";
            
            // تعبئة البيانات الأساسية
            TxtInvoiceNumber.Text = _currentInvoice.InvoiceNumber;
            TxtAmount.Text = _currentInvoice.Amount.ToString("F2");
            TxtPaidAmount.Text = _currentInvoice.PaidAmount.ToString("F2");
            TxtDescription.Text = _currentInvoice.Description;
            TxtFilePath.Text = _currentInvoice.FilePath;
            DpIssueDate.SelectedDate = _currentInvoice.IssueDate;
            DpDueDate.SelectedDate = _currentInvoice.DueDate;

            // تحديد المشروع والارتباط
            if (_currentInvoice.ProjectId.HasValue)
            {
                CmbProject.SelectedValue = _currentInvoice.ProjectId.Value;
            }
            
            if (_currentInvoice.CommitmentId.HasValue)
            {
                CmbCommitment.SelectedValue = _currentInvoice.CommitmentId.Value;
            }

            // تحديد نوع الفاتورة
            foreach (ComboBoxItem item in CmbInvoiceType.Items)
            {
                if (item.Tag?.ToString() == _currentInvoice.Type)
                {
                    CmbInvoiceType.SelectedItem = item;
                    break;
                }
            }

            // تحديد حالة الفاتورة
            foreach (ComboBoxItem item in CmbInvoiceStatus.Items)
            {
                if (item.Tag?.ToString() == _currentInvoice.Status)
                {
                    CmbInvoiceStatus.SelectedItem = item;
                    break;
                }
            }

            // عرض المعلومات الإضافية
            ExpanderAdditionalInfo.Visibility = Visibility.Visible;
            TxtCreatedDate.Text = _currentInvoice.CreatedDate.ToString("dd/MM/yyyy HH:mm");
            TxtLastModified.Text = _currentInvoice.LastModifiedDate?.ToString("dd/MM/yyyy HH:mm") ?? "لم يتم التحديث";
            
            UpdateCalculatedFields();
            UpdateFileButtons();
        }

        /// <summary>
        /// تحديث المبلغ المستحق عند تغيير المبلغ
        /// </summary>
        private void TxtAmount_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateOutstandingAmount();
        }

        /// <summary>
        /// تحديث المبلغ المستحق عند تغيير المبلغ المدفوع
        /// </summary>
        private void TxtPaidAmount_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateOutstandingAmount();
        }

        /// <summary>
        /// تحديث المبلغ المستحق
        /// </summary>
        private void UpdateOutstandingAmount()
        {
            if (decimal.TryParse(TxtAmount.Text, out decimal amount) &&
                decimal.TryParse(TxtPaidAmount.Text, out decimal paidAmount))
            {
                var outstanding = amount - paidAmount;
                TxtOutstandingAmount.Text = outstanding.ToString("F2");
            }
            else
            {
                TxtOutstandingAmount.Text = "0.00";
            }

            if (_isEditMode)
            {
                UpdateCalculatedFields();
            }
        }

        /// <summary>
        /// تحديث الحقول المحسوبة
        /// </summary>
        private void UpdateCalculatedFields()
        {
            if (_currentInvoice == null) return;

            if (decimal.TryParse(TxtAmount.Text, out decimal amount) && amount > 0 &&
                decimal.TryParse(TxtPaidAmount.Text, out decimal paidAmount))
            {
                var paymentPercentage = (paidAmount / amount) * 100;
                TxtPaymentPercentage.Text = $"{paymentPercentage:F1}%";
            }

            if (DpDueDate.SelectedDate.HasValue)
            {
                var daysUntilDue = (DpDueDate.SelectedDate.Value - DateTime.Now).Days;
                TxtDaysUntilDue.Text = daysUntilDue.ToString();
            }
        }

        /// <summary>
        /// اختيار ملف
        /// </summary>
        private async void BtnSelectFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var filter = "PDF Files (*.pdf)|*.pdf|Word Documents (*.docx;*.doc)|*.docx;*.doc|Excel Files (*.xlsx;*.xls)|*.xlsx;*.xls|All Files (*.*)|*.*";
                var selectedFile = _fileService.SelectFile(filter);
                
                if (!string.IsNullOrEmpty(selectedFile))
                {
                    // نسخ الملف إلى مجلد الفواتير
                    var fileName = System.IO.Path.GetFileName(selectedFile);
                    var relativePath = await _fileService.SaveFileAsync(selectedFile, "invoices", fileName);
                    
                    TxtFilePath.Text = relativePath;
                    UpdateFileButtons();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء اختيار الملف:\n{ex.Message}", 
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// فتح الملف
        /// </summary>
        private async void BtnOpenFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(TxtFilePath.Text))
                {
                    var success = await _fileService.OpenFileAsync(TxtFilePath.Text);
                    if (!success)
                    {
                        MessageBox.Show("فشل في فتح الملف. تأكد من وجود الملف.", 
                                      "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح الملف:\n{ex.Message}", 
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث حالة أزرار الملف
        /// </summary>
        private void UpdateFileButtons()
        {
            BtnOpenFile.IsEnabled = !string.IsNullOrEmpty(TxtFilePath.Text) && 
                                   _fileService.FileExists(TxtFilePath.Text);
        }

        /// <summary>
        /// حفظ الفاتورة
        /// </summary>
        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                // إنشاء أو تحديث الفاتورة
                var invoice = _isEditMode ? _currentInvoice! : new Invoice();
                
                // تعبئة البيانات
                invoice.InvoiceNumber = TxtInvoiceNumber.Text.Trim();
                invoice.Amount = decimal.Parse(TxtAmount.Text);
                invoice.PaidAmount = decimal.TryParse(TxtPaidAmount.Text, out decimal paid) ? paid : 0;
                invoice.Description = string.IsNullOrWhiteSpace(TxtDescription.Text) 
                    ? null : TxtDescription.Text.Trim();
                invoice.FilePath = string.IsNullOrWhiteSpace(TxtFilePath.Text) 
                    ? null : TxtFilePath.Text.Trim();
                invoice.IssueDate = DpIssueDate.SelectedDate ?? DateTime.Now;
                invoice.DueDate = DpDueDate.SelectedDate;

                if (CmbProject.SelectedValue != null)
                {
                    invoice.ProjectId = (int)CmbProject.SelectedValue;
                }

                if (CmbCommitment.SelectedValue != null)
                {
                    invoice.CommitmentId = (int)CmbCommitment.SelectedValue;
                }

                if (CmbInvoiceType.SelectedItem is ComboBoxItem typeItem)
                {
                    invoice.Type = typeItem.Tag?.ToString() ?? "Task";
                }

                if (CmbInvoiceStatus.SelectedItem is ComboBoxItem statusItem)
                {
                    invoice.Status = statusItem.Tag?.ToString() ?? "Pending";
                }

                // حفظ في قاعدة البيانات
                if (_isEditMode)
                {
                    Result = await _dataService.UpdateInvoiceAsync(invoice);
                }
                else
                {
                    Result = await _dataService.AddInvoiceAsync(invoice);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ الفاتورة:\n{ex.Message}", 
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// التحقق من صحة البيانات المدخلة
        /// </summary>
        private bool ValidateInput()
        {
            // التحقق من رقم الفاتورة
            if (string.IsNullOrWhiteSpace(TxtInvoiceNumber.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الفاتورة", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtInvoiceNumber.Focus();
                return false;
            }

            // التحقق من مبلغ الفاتورة
            if (!decimal.TryParse(TxtAmount.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح للفاتورة", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtAmount.Focus();
                return false;
            }

            // التحقق من المبلغ المدفوع
            if (!string.IsNullOrWhiteSpace(TxtPaidAmount.Text))
            {
                if (!decimal.TryParse(TxtPaidAmount.Text, out decimal paidAmount) || paidAmount < 0)
                {
                    MessageBox.Show("يرجى إدخال مبلغ صحيح للمبلغ المدفوع", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtPaidAmount.Focus();
                    return false;
                }

                if (paidAmount > amount)
                {
                    MessageBox.Show("المبلغ المدفوع لا يمكن أن يكون أكبر من مبلغ الفاتورة", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtPaidAmount.Focus();
                    return false;
                }
            }

            // التحقق من تاريخ الإصدار
            if (!DpIssueDate.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ الإصدار", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                DpIssueDate.Focus();
                return false;
            }

            // التحقق من التواريخ
            if (DpIssueDate.SelectedDate.HasValue && DpDueDate.SelectedDate.HasValue)
            {
                if (DpIssueDate.SelectedDate.Value > DpDueDate.SelectedDate.Value)
                {
                    MessageBox.Show("تاريخ الإصدار يجب أن يكون قبل تاريخ الاستحقاق", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    DpIssueDate.Focus();
                    return false;
                }
            }

            // التحقق من نوع الفاتورة
            if (CmbInvoiceType.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع الفاتورة", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbInvoiceType.Focus();
                return false;
            }

            // التحقق من حالة الفاتورة
            if (CmbInvoiceStatus.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار حالة الفاتورة", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbInvoiceStatus.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// عرض نافذة إضافة فاتورة جديدة
        /// </summary>
        public static Invoice? ShowAddDialog(Window owner, IDataService dataService, IFileService fileService)
        {
            var dialog = new InvoiceDialog(dataService, fileService)
            {
                Owner = owner
            };

            return dialog.ShowDialog() == true ? dialog.Result : null;
        }

        /// <summary>
        /// عرض نافذة تعديل فاتورة موجودة
        /// </summary>
        public static Invoice? ShowEditDialog(Window owner, IDataService dataService, IFileService fileService, Invoice invoice)
        {
            var dialog = new InvoiceDialog(dataService, fileService, invoice)
            {
                Owner = owner
            };

            return dialog.ShowDialog() == true ? dialog.Result : null;
        }
    }
}
