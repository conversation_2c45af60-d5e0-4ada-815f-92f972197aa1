# دليل التثبيت - Financial Tracker

## متطلبات النظام

### الحد الأدنى
- **نظام التشغيل**: Windows 10 (1809) أو أحدث
- **المعمارية**: x64 أو x86
- **الذاكرة**: 2 GB RAM
- **مساحة القرص**: 500 MB مساحة فارغة
- **.NET Runtime**: .NET 6.0 أو أحدث

### الموصى به
- **نظام التشغيل**: Windows 11
- **المعمارية**: x64
- **الذاكرة**: 4 GB RAM أو أكثر
- **مساحة القرص**: 1 GB مساحة فارغة
- **الدقة**: 1920x1080 أو أعلى

## طرق التثبيت

### الطريقة الأولى: تشغيل مباشر (للمطورين)

#### 1. تثبيت .NET 6 SDK
```bash
# تحميل من الموقع الرسمي
https://dotnet.microsoft.com/download/dotnet/6.0
```

#### 2. استنساخ المشروع
```bash
git clone [repository-url]
cd FinancialTracker
```

#### 3. استعادة الحزم
```bash
dotnet restore
```

#### 4. بناء المشروع
```bash
dotnet build
```

#### 5. تشغيل التطبيق
```bash
dotnet run
```

### الطريقة الثانية: نشر مستقل

#### 1. بناء للنشر
```bash
# نشر مع .NET Runtime مدمج
dotnet publish -c Release -r win-x64 --self-contained true

# أو نشر بدون .NET Runtime (يتطلب تثبيت .NET 6)
dotnet publish -c Release -r win-x64 --self-contained false
```

#### 2. نسخ الملفات
- انسخ مجلد `bin/Release/net6.0-windows/win-x64/publish/`
- ضعه في المجلد المطلوب

#### 3. تشغيل التطبيق
- شغل `FinancialTracker.exe`

### الطريقة الثالثة: استخدام ملف التشغيل السريع

#### 1. تشغيل ملف Batch
```cmd
# في مجلد المشروع
run.bat
```

## إعداد قاعدة البيانات

### إنشاء تلقائي
- قاعدة البيانات تُنشأ تلقائياً عند أول تشغيل
- الملف: `FinancialTracker.db` في مجلد التطبيق
- البيانات الأولية تُضاف تلقائياً

### إنشاء يدوي (للمطورين)
```bash
# إنشاء Migration جديد
dotnet ef migrations add InitialCreate

# تطبيق Migration على قاعدة البيانات
dotnet ef database update
```

## إعداد الملفات

### مجلدات التطبيق
سيتم إنشاء المجلدات التالية تلقائياً:
```
FinancialTracker/
├── FinancialTracker.exe    # الملف التنفيذي
├── FinancialTracker.db     # قاعدة البيانات
├── Documents/              # مجلد الوثائق
│   ├── Invoices/          # فواتير
│   ├── Commitments/       # ارتباطات
│   ├── Replies/           # ردود
│   ├── Projects/          # مشاريع
│   └── Backup/            # نسخ احتياطية
└── Logs/                  # ملفات السجل (مستقبلي)
```

## استكشاف مشاكل التثبيت

### مشكلة: التطبيق لا يبدأ

#### الأعراض
```
Application failed to start
```

#### الحلول
1. **تأكد من تثبيت .NET 6**
   ```bash
   dotnet --version
   ```

2. **تشغيل كمسؤول**
   - انقر بالزر الأيمن على التطبيق
   - اختر "تشغيل كمسؤول"

3. **فحص ملفات النظام**
   ```cmd
   sfc /scannow
   ```

### مشكلة: خطأ في قاعدة البيانات

#### الأعراض
```
Microsoft.Data.Sqlite.SqliteException
```

#### الحلول
1. **تأكد من صلاحيات الكتابة**
   - تأكد أن المجلد قابل للكتابة
   - شغل التطبيق كمسؤول

2. **حذف قاعدة البيانات**
   ```bash
   # احذف الملف واتركه ينشأ من جديد
   del FinancialTracker.db
   ```

3. **فحص مساحة القرص**
   - تأكد من وجود مساحة كافية

### مشكلة: واجهة المستخدم لا تظهر صحيحاً

#### الأعراض
- النصوص العربية لا تظهر
- التصميم مشوه

#### الحلول
1. **تحديث Windows**
   ```cmd
   # تشغيل Windows Update
   ```

2. **تثبيت خطوط عربية**
   - تأكد من وجود خطوط عربية في النظام

3. **تحديث برامج التشغيل**
   - حدث برامج تشغيل كرت الشاشة

### مشكلة: الملفات لا تُرفع

#### الأعراض
```
Access denied when uploading files
```

#### الحلول
1. **فحص الصلاحيات**
   - تأكد من صلاحيات الكتابة في مجلد Documents

2. **فحص مكافح الفيروسات**
   - أضف التطبيق لقائمة الاستثناءات

3. **فحص مساحة القرص**
   - تأكد من وجود مساحة كافية

## إلغاء التثبيت

### إزالة التطبيق
1. **احذف مجلد التطبيق**
2. **احذف البيانات (اختياري)**
   - `FinancialTracker.db`
   - مجلد `Documents/`

### الاحتفاظ بالبيانات
- انسخ `FinancialTracker.db` قبل الحذف
- انسخ مجلد `Documents/` للاحتفاظ بالملفات

## التحديث

### تحديث التطبيق
1. **احتفظ بنسخة احتياطية**
   ```bash
   # انسخ قاعدة البيانات والملفات
   copy FinancialTracker.db backup/
   xcopy Documents backup/Documents /E
   ```

2. **استبدل الملفات**
   - استبدل الملف التنفيذي الجديد
   - احتفظ بقاعدة البيانات والملفات

3. **اختبر التطبيق**
   - شغل التطبيق وتأكد من عمله

### ترقية قاعدة البيانات
- التطبيق يدعم الترقية التلقائية
- البيانات الموجودة ستبقى محفوظة

## الدعم الفني

### الحصول على المساعدة
- **GitHub Issues**: [رابط المشروع]
- **البريد الإلكتروني**: [البريد الإلكتروني]
- **التوثيق**: راجع `README.md` و `DEVELOPER_GUIDE.md`

### الإبلاغ عن مشاكل
عند الإبلاغ عن مشكلة، يرجى تضمين:
- إصدار Windows
- إصدار .NET
- رسالة الخطأ كاملة
- خطوات إعادة إنتاج المشكلة

---

**ملاحظة**: هذا الدليل يُحدث مع كل إصدار جديد من التطبيق.
