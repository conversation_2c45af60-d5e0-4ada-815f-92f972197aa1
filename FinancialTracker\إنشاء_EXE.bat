@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    إنشاء ملف EXE - Financial Tracker
echo ========================================
echo.

echo [1/4] تنظيف المشروع...
dotnet clean >nul 2>&1

echo [2/4] استعادة الحزم...
dotnet restore >nul 2>&1

echo [3/4] بناء المشروع...
dotnet build -c Release >nul 2>&1

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع!
    echo تأكد من تثبيت .NET 6 SDK
    pause
    exit /b 1
)

echo [4/4] إنشاء ملف EXE...
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -o "./publish" >nul 2>&1

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم إنشاء ملف EXE بنجاح!
    echo 📁 المسار: .\publish\FinancialTracker.exe
    echo.
    if exist ".\publish\FinancialTracker.exe" (
        for %%A in (".\publish\FinancialTracker.exe") do (
            set size=%%~zA
            set /a sizeMB=!size!/1048576
            echo 📊 حجم الملف: !sizeMB! MB تقريباً
        )
    )
    echo.
    echo هل تريد تشغيل التطبيق الآن؟
    echo اضغط Y للتشغيل أو أي مفتاح آخر للخروج
    choice /c YN /n /m "[Y/N]: "
    if !errorlevel!==1 (
        echo.
        echo 🚀 جاري تشغيل التطبيق...
        start "" ".\publish\FinancialTracker.exe"
    )
) else (
    echo.
    echo ❌ فشل في إنشاء ملف EXE!
    echo تحقق من:
    echo - تثبيت .NET 6 SDK
    echo - وجود مساحة كافية على القرص
    echo - إغلاق مكافح الفيروسات مؤقتاً
)

echo.
pause
