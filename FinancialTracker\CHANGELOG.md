# سجل التغييرات - Financial Tracker

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

التنسيق مبني على [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)،
وهذا المشروع يتبع [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2025-07-13

### إضافات جديدة
- ✨ نظام إدارة المشاريع المالية الكامل
- 🏗️ إدارة المشاريع مع تتبع الحالة والميزانية
- 📄 إدارة الفواتير مع ربطها بالمشاريع والارتباطات
- 🔗 نظام الارتباطات المالية
- 💬 نظام الردود على الفواتير والارتباطات
- 📊 لوحة معلومات تفاعلية مع إحصائيات شاملة
- 📁 نظام إدارة الملفات المرفقة
- 🎨 واجهة مستخدم بتصميم Material Design
- 🌐 دعم كامل للغة العربية مع RTL
- 🗃️ قاعدة بيانات SQLite مع Entity Framework Core
- 🔍 نظام بحث وفلترة متقدم

### التقنيات المستخدمة
- .NET 6 WPF Application
- SQLite Database with Entity Framework Core 6.0
- Material Design in XAML UI Framework
- Microsoft.Extensions.DependencyInjection
- MVVM Architecture Pattern
- Arabic RTL Support

### الميزات الأساسية
- إنشاء وتعديل وحذف المشاريع
- إدارة الفواتير مع تتبع المدفوعات
- نظام الارتباطات المالية
- إدارة الردود والمراسلات
- تقارير مالية تفصيلية
- رفع وإدارة الملفات
- بيانات أولية تجريبية

### الملفات الرئيسية
- `MainWindow.xaml` - النافذة الرئيسية
- `DashboardWindow.xaml` - لوحة المعلومات
- `ProjectDialog.xaml` - نافذة إدارة المشاريع
- `InvoiceDialog.xaml` - نافذة إدارة الفواتير
- `CommitmentDialog.xaml` - نافذة إدارة الارتباطات
- `FinancialContext.cs` - سياق قاعدة البيانات
- `DataService.cs` - خدمة البيانات
- `FileService.cs` - خدمة الملفات

### البيانات الأولية
- 5 مشاريع تجريبية (UMS, BNG, AAA, NTP, HPBX)
- 12 ارتباط مالي
- 25+ فاتورة بحالات مختلفة
- 16 رد على الفواتير والارتباطات

### التوثيق
- `README.md` - دليل المستخدم الشامل
- `DEVELOPER_GUIDE.md` - دليل المطور
- `CHANGELOG.md` - سجل التغييرات
- ملفات إعداد Visual Studio Code

### ملفات التشغيل
- `run.bat` - ملف تشغيل سريع
- `appsettings.json` - إعدادات التطبيق
- `.gitignore` - ملف Git ignore
- `LICENSE` - ترخيص MIT

## الإصدارات المستقبلية

### [1.1.0] - مخطط له
#### إضافات مخططة
- [ ] تصدير التقارير إلى PDF/Excel
- [ ] نظام إشعارات للفواتير المتأخرة
- [ ] تحسينات في واجهة المستخدم
- [ ] إضافة Dark Theme
- [ ] تحسين الأداء

### [1.2.0] - مخطط له
#### إضافات مخططة
- [ ] نظام صلاحيات المستخدمين
- [ ] تسجيل العمليات (Audit Log)
- [ ] تكامل مع البريد الإلكتروني
- [ ] واجهة ويب للوصول عن بُعد

### [2.0.0] - مخطط له
#### إضافات مخططة
- [ ] إعادة هيكلة البنية للدعم المتعدد المستخدمين
- [ ] تكامل مع أنظمة المحاسبة الخارجية
- [ ] API للتكامل مع التطبيقات الأخرى
- [ ] تطبيق موبايل مصاحب

---

## تنسيق سجل التغييرات

### أنواع التغييرات
- `إضافات جديدة` للميزات الجديدة
- `تغييرات` للتغييرات في الميزات الموجودة
- `إهمال` للميزات التي ستُزال قريباً
- `إزالة` للميزات المُزالة
- `إصلاحات` لإصلاح الأخطاء
- `أمان` لإصلاحات الأمان

### الرموز المستخدمة
- ✨ ميزة جديدة
- 🔧 تحسين
- 🐛 إصلاح خطأ
- 📚 توثيق
- 🎨 تحسين واجهة المستخدم
- ⚡ تحسين الأداء
- 🔒 أمان
- 🗃️ قاعدة البيانات
- 🌐 دعم اللغات
