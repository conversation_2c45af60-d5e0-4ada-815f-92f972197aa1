# دليل المطور - Financial Tracker

## نظرة عامة على البنية

### نمط MVVM
يستخدم المشروع نمط Model-View-ViewModel مع:
- **Models**: نماذج البيانات في مجلد `Models/`
- **Views**: واجهات المستخدم (XAML) في `Views/` و `Dialogs/`
- **ViewModels**: منطق العرض (مدمج في Code-Behind للبساطة)

### حقن التبعيات
- يستخدم `Microsoft.Extensions.DependencyInjection`
- التكوين في `App.xaml.cs`
- الخدمات المسجلة: `IDataService`, `IFileService`, `FinancialContext`

## هيكل قاعدة البيانات

### الجداول الرئيسية
1. **Projects** - المشاريع
2. **Invoices** - الفواتير
3. **Commitments** - الارتباطات
4. **Replies** - الردود

### العلاقات
- Project → Invoices (One-to-Many)
- Project → Commitments (One-to-Many)
- Commitment → Invoices (One-to-Many)
- Invoice → Replies (One-to-Many)
- Commitment → Replies (One-to-Many)

## إضافة ميزة جديدة

### 1. إنشاء النموذج
```csharp
// في Models/
public class NewEntity
{
    public int Id { get; set; }
    public string Name { get; set; }
    // خصائص أخرى...
}
```

### 2. تحديث DbContext
```csharp
// في FinancialContext.cs
public DbSet<NewEntity> NewEntities { get; set; }

// في OnModelCreating
modelBuilder.Entity<NewEntity>(entity =>
{
    entity.HasKey(e => e.Id);
    // تكوين إضافي...
});
```

### 3. إضافة خدمات
```csharp
// في IDataService.cs
Task<List<NewEntity>> GetNewEntitiesAsync();
Task<NewEntity> AddNewEntityAsync(NewEntity entity);

// في DataService.cs
public async Task<List<NewEntity>> GetNewEntitiesAsync()
{
    return await _context.NewEntities.ToListAsync();
}
```

### 4. إنشاء نافذة حوار
- إنشاء `NewEntityDialog.xaml` في `Dialogs/`
- تطبيق `NewEntityDialog.xaml.cs`
- استخدام Material Design controls

### 5. إضافة Migration
```bash
dotnet ef migrations add AddNewEntity
dotnet ef database update
```

## إرشادات التطوير

### تسمية الملفات
- **Models**: `EntityName.cs`
- **Views**: `EntityNameWindow.xaml`
- **Dialogs**: `EntityNameDialog.xaml`
- **Services**: `IEntityNameService.cs`, `EntityNameService.cs`

### تسمية المتغيرات
- **C#**: PascalCase للخصائص، camelCase للمتغيرات المحلية
- **XAML**: PascalCase للعناصر والخصائص

### التعليقات
- استخدم تعليقات XML للطرق العامة
- اكتب التعليقات باللغة العربية للوضوح

### معالجة الأخطاء
```csharp
try
{
    // العملية
}
catch (Exception ex)
{
    UpdateStatusMessage($"خطأ: {ex.Message}");
    MessageBox.Show($"حدث خطأ:\n{ex.Message}", 
                  "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
}
```

## اختبار التطبيق

### اختبار يدوي
1. تشغيل التطبيق: `dotnet run`
2. اختبار إضافة مشروع جديد
3. اختبار إضافة فاتورة مرتبطة
4. اختبار إضافة ارتباط
5. اختبار لوحة المعلومات
6. اختبار البحث والفلترة

### نقاط الاختبار المهمة
- [ ] إنشاء قاعدة البيانات تلقائياً
- [ ] تحميل البيانات الأولية
- [ ] عمل جميع النوافذ
- [ ] حفظ واسترجاع البيانات
- [ ] رفع وفتح الملفات
- [ ] عرض الإحصائيات
- [ ] البحث والفلترة

## استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في قاعدة البيانات
```
Microsoft.Data.Sqlite.SqliteException
```
**الحل**: تأكد من وجود مجلد التطبيق وصلاحيات الكتابة

#### خطأ في Material Design
```
Could not load file or assembly 'MaterialDesignThemes.Wpf'
```
**الحل**: `dotnet restore` ثم `dotnet build`

#### خطأ في حقن التبعيات
```
InvalidOperationException: Service not found
```
**الحل**: تأكد من تسجيل الخدمة في `App.xaml.cs`

### تسجيل الأخطاء
- الأخطاء تظهر في MessageBox
- رسائل الحالة في شريط الحالة
- يمكن إضافة نظام تسجيل متقدم لاحقاً

## نشر التطبيق

### نشر محلي
```bash
dotnet publish -c Release -r win-x64 --self-contained
```

### نشر مع .NET Runtime
```bash
dotnet publish -c Release -r win-x64 --no-self-contained
```

### ملفات النشر
- الملف التنفيذي: `FinancialTracker.exe`
- قاعدة البيانات: `FinancialTracker.db` (تُنشأ تلقائياً)
- مجلد الوثائق: `Documents/` (يُنشأ تلقائياً)

## التحسينات المستقبلية

### الأداء
- إضافة Lazy Loading للبيانات الكبيرة
- تحسين استعلامات قاعدة البيانات
- إضافة Caching للبيانات المتكررة

### الأمان
- تشفير قاعدة البيانات
- نظام صلاحيات المستخدمين
- تسجيل العمليات (Audit Log)

### الواجهة
- إضافة Dark Theme
- تحسين الاستجابة للشاشات المختلفة
- إضافة رسوم بيانية تفاعلية

### الوظائف
- تصدير التقارير
- استيراد البيانات من Excel
- نظام إشعارات
- تكامل مع البريد الإلكتروني

## الموارد المفيدة

### التوثيق
- [.NET 6 Documentation](https://docs.microsoft.com/en-us/dotnet/)
- [WPF Documentation](https://docs.microsoft.com/en-us/dotnet/desktop/wpf/)
- [Material Design in XAML](http://materialdesigninxaml.net/)
- [Entity Framework Core](https://docs.microsoft.com/en-us/ef/core/)

### أدوات التطوير
- **Visual Studio 2022** - IDE الرئيسي
- **Visual Studio Code** - محرر خفيف
- **DB Browser for SQLite** - لفحص قاعدة البيانات
- **Git** - إدارة الإصدارات

---

**ملاحظة**: هذا الدليل يتطور مع المشروع. يرجى تحديثه عند إضافة ميزات جديدة.
