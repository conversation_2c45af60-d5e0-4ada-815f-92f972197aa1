@echo off
echo ========================================
echo    Creating EXE File - Financial Tracker
echo ========================================
echo.

echo [1/4] Cleaning project...
dotnet clean

echo.
echo [2/4] Restoring packages...
dotnet restore

echo.
echo [3/4] Building project...
dotnet build -c Release

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Build failed!
    echo Make sure .NET 6 SDK is installed
    pause
    exit /b 1
)

echo.
echo [4/4] Creating EXE file...
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -o "./publish"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ EXE file created successfully!
    echo 📁 Location: .\publish\FinancialTracker.exe
    echo.
    echo Would you like to run the application now? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo.
        echo 🚀 Starting application...
        start "" ".\publish\FinancialTracker.exe"
    )
) else (
    echo.
    echo ❌ Failed to create EXE file!
    echo Please check the errors above.
)

echo.
pause
