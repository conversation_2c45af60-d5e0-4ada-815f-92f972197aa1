<Window x:Class="FinancialTracker.Dialogs.CommitmentDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="إدارة الارتباط" 
        Height="600" Width="500"
        MinHeight="500" MinWidth="450"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="{StaticResource ArabicFont}"
        Background="{StaticResource BackgroundBrush}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        ResizeMode="CanResize">

    <materialDesign:DialogHost Identifier="CommitmentDialog">
        <Grid Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- العنوان -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
                <materialDesign:PackIcon Kind="Link" 
                                       Width="32" Height="32"
                                       VerticalAlignment="Center"
                                       Foreground="{StaticResource SecondaryBrush}"/>
                <TextBlock x:Name="TxtDialogTitle"
                         Text="إضافة ارتباط جديد" 
                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                         VerticalAlignment="Center"
                         Margin="12,0,0,0"/>
            </StackPanel>

            <!-- محتوى النموذج -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- رقم الارتباط -->
                    <TextBox x:Name="TxtCommitmentNumber"
                           Style="{StaticResource ArabicTextBoxStyle}"
                           materialDesign:HintAssist.Hint="رقم الارتباط *"
                           materialDesign:HintAssist.IsFloating="True"
                           MaxLength="100"
                           Margin="0,0,0,16"/>

                    <!-- المشروع المرتبط -->
                    <ComboBox x:Name="CmbProject"
                            Style="{StaticResource MaterialDesignComboBox}"
                            materialDesign:HintAssist.Hint="المشروع المرتبط"
                            materialDesign:HintAssist.IsFloating="True"
                            FlowDirection="RightToLeft"
                            FontFamily="{StaticResource ArabicFont}"
                            DisplayMemberPath="Name"
                            SelectedValuePath="Id"
                            Margin="0,0,0,16"/>

                    <!-- مبلغ الارتباط -->
                    <TextBox x:Name="TxtAmount"
                           Style="{StaticResource ArabicTextBoxStyle}"
                           materialDesign:HintAssist.Hint="مبلغ الارتباط (ج.م) *"
                           materialDesign:HintAssist.IsFloating="True"
                           Margin="0,0,0,16"/>

                    <!-- تاريخ الإصدار -->
                    <DatePicker x:Name="DpIssueDate"
                              Style="{StaticResource MaterialDesignDatePicker}"
                              materialDesign:HintAssist.Hint="تاريخ الإصدار *"
                              materialDesign:HintAssist.IsFloating="True"
                              FlowDirection="RightToLeft"
                              Margin="0,0,0,16"/>

                    <!-- نوع الارتباط -->
                    <ComboBox x:Name="CmbCommitmentType"
                            Style="{StaticResource MaterialDesignComboBox}"
                            materialDesign:HintAssist.Hint="نوع الارتباط *"
                            materialDesign:HintAssist.IsFloating="True"
                            FlowDirection="RightToLeft"
                            FontFamily="{StaticResource ArabicFont}"
                            Margin="0,0,0,16">
                        <ComboBoxItem Content="مهمة" Tag="Task"/>
                        <ComboBoxItem Content="خدمة" Tag="Service"/>
                        <ComboBoxItem Content="أخرى" Tag="Other"/>
                    </ComboBox>

                    <!-- حالة الارتباط -->
                    <ComboBox x:Name="CmbCommitmentStatus"
                            Style="{StaticResource MaterialDesignComboBox}"
                            materialDesign:HintAssist.Hint="حالة الارتباط *"
                            materialDesign:HintAssist.IsFloating="True"
                            FlowDirection="RightToLeft"
                            FontFamily="{StaticResource ArabicFont}"
                            Margin="0,0,0,16">
                        <ComboBoxItem Content="نشط" Tag="Active"/>
                        <ComboBoxItem Content="مكتمل" Tag="Completed"/>
                        <ComboBoxItem Content="ملغي" Tag="Cancelled"/>
                    </ComboBox>

                    <!-- وصف الارتباط -->
                    <TextBox x:Name="TxtDescription"
                           Style="{StaticResource ArabicTextBoxStyle}"
                           materialDesign:HintAssist.Hint="وصف الارتباط"
                           materialDesign:HintAssist.IsFloating="True"
                           AcceptsReturn="True"
                           TextWrapping="Wrap"
                           MaxLength="1000"
                           Height="80"
                           VerticalScrollBarVisibility="Auto"
                           Margin="0,0,0,16"/>

                    <!-- ملف الارتباط -->
                    <Grid Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBox x:Name="TxtFilePath"
                               Grid.Column="0"
                               Style="{StaticResource ArabicTextBoxStyle}"
                               materialDesign:HintAssist.Hint="ملف الارتباط"
                               materialDesign:HintAssist.IsFloating="True"
                               IsReadOnly="True"/>

                        <Button x:Name="BtnSelectFile"
                              Grid.Column="1"
                              Style="{StaticResource MaterialDesignIconButton}"
                              ToolTip="اختيار ملف"
                              Click="BtnSelectFile_Click"
                              Margin="8,0,0,0">
                            <materialDesign:PackIcon Kind="FileSearch" 
                                                   Width="20" Height="20"/>
                        </Button>

                        <Button x:Name="BtnOpenFile"
                              Grid.Column="2"
                              Style="{StaticResource MaterialDesignIconButton}"
                              ToolTip="فتح الملف"
                              Click="BtnOpenFile_Click"
                              IsEnabled="False"
                              Margin="4,0,0,0">
                            <materialDesign:PackIcon Kind="FileOpen" 
                                                   Width="20" Height="20"/>
                        </Button>
                    </Grid>

                    <!-- نسبة الإنجاز -->
                    <StackPanel Margin="0,0,0,16">
                        <TextBlock Text="نسبة الإنجاز (%)" 
                                 Style="{StaticResource MaterialDesignBody2TextBlock}"
                                 Margin="0,0,0,8"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <Slider x:Name="SliderCompletion"
                                  Grid.Column="0"
                                  Style="{StaticResource MaterialDesignSlider}"
                                  Minimum="0"
                                  Maximum="100"
                                  Value="0"
                                  TickFrequency="10"
                                  IsSnapToTickEnabled="True"
                                  ValueChanged="SliderCompletion_ValueChanged"/>
                            
                            <TextBlock x:Name="TxtCompletionValue"
                                     Grid.Column="1"
                                     Text="0%"
                                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                                     VerticalAlignment="Center"
                                     Margin="8,0,0,0"
                                     MinWidth="40"/>
                        </Grid>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>

            <!-- أزرار الإجراءات -->
            <StackPanel Grid.Row="2" 
                      Orientation="Horizontal" 
                      HorizontalAlignment="Left" 
                      Margin="0,16,0,0">
                
                <Button x:Name="BtnSave"
                      Content="حفظ"
                      Style="{StaticResource ArabicButtonStyle}"
                      Background="{StaticResource SecondaryBrush}"
                      Click="BtnSave_Click"
                      Margin="0,0,8,0"
                      MinWidth="100">
                    <Button.ContentTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ContentSave" 
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="{Binding}" 
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </DataTemplate>
                    </Button.ContentTemplate>
                </Button>

                <Button x:Name="BtnCancel"
                      Content="إلغاء"
                      Style="{StaticResource MaterialDesignOutlinedButton}"
                      Click="BtnCancel_Click"
                      Margin="8,0,0,0"
                      MinWidth="100">
                    <Button.ContentTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Cancel" 
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="{Binding}" 
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </DataTemplate>
                    </Button.ContentTemplate>
                </Button>
            </StackPanel>
        </Grid>
    </materialDesign:DialogHost>
</Window>
