using FinancialTracker.Models;
using FinancialTracker.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace FinancialTracker.Views
{
    /// <summary>
    /// منطق التفاعل لنافذة لوحة المعلومات
    /// </summary>
    public partial class DashboardWindow : Window
    {
        private readonly IDataService _dataService;

        /// <summary>
        /// منشئ نافذة لوحة المعلومات
        /// </summary>
        public DashboardWindow(IDataService dataService)
        {
            InitializeComponent();
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            
            LoadDataAsync();
        }

        /// <summary>
        /// تحميل جميع البيانات والإحصائيات
        /// </summary>
        private async void LoadDataAsync()
        {
            try
            {
                // تحميل الإحصائيات الأساسية
                await LoadBasicStatisticsAsync();
                
                // تحميل الإحصائيات المالية
                await LoadFinancialStatisticsAsync();
                
                // تحميل إحصائيات المشاريع والفواتير
                await LoadProjectAndInvoiceStatisticsAsync();
                
                // تحميل ملخص المشاريع
                await LoadProjectSummariesAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات:\n{ex.Message}", 
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل الإحصائيات الأساسية
        /// </summary>
        private async Task LoadBasicStatisticsAsync()
        {
            try
            {
                // عدد المشاريع
                var totalProjects = await _dataService.GetTotalProjectsCountAsync();
                TxtTotalProjects.Text = totalProjects.ToString();

                // عدد الفواتير
                var invoices = await _dataService.GetInvoicesAsync();
                TxtTotalInvoices.Text = invoices.Count.ToString();

                // عدد الارتباطات
                var commitments = await _dataService.GetCommitmentsAsync();
                TxtTotalCommitments.Text = commitments.Count.ToString();

                // عدد الفواتير المتأخرة
                var overdueInvoices = await _dataService.GetOverdueInvoicesAsync();
                TxtOverdueInvoices.Text = overdueInvoices.Count.ToString();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحميل الإحصائيات الأساسية: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل الإحصائيات المالية
        /// </summary>
        private async Task LoadFinancialStatisticsAsync()
        {
            try
            {
                var totalInvoiceAmount = await _dataService.GetTotalInvoiceAmountAsync();
                var totalPaidAmount = await _dataService.GetTotalPaidAmountAsync();
                var totalOutstandingAmount = await _dataService.GetTotalOutstandingAmountAsync();

                TxtTotalInvoiceAmount.Text = $"{totalInvoiceAmount:N2} ج.م";
                TxtTotalPaidAmount.Text = $"{totalPaidAmount:N2} ج.م";
                TxtTotalOutstandingAmount.Text = $"{totalOutstandingAmount:N2} ج.م";
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحميل الإحصائيات المالية: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل إحصائيات المشاريع والفواتير
        /// </summary>
        private async Task LoadProjectAndInvoiceStatisticsAsync()
        {
            try
            {
                // إحصائيات المشاريع حسب الحالة
                var projects = await _dataService.GetProjectsAsync();
                var projectStats = projects
                    .GroupBy(p => p.Status)
                    .Select(g => new { Status = GetStatusDisplayName(g.Key), Count = g.Count() })
                    .ToList();
                
                ProjectStatusStats.ItemsSource = projectStats;

                // إحصائيات الفواتير حسب الحالة
                var invoiceStats = await _dataService.GetInvoiceStatsByStatusAsync();
                var invoiceStatsDisplay = invoiceStats
                    .Select(kvp => new { Status = GetStatusDisplayName(kvp.Key), Count = kvp.Value })
                    .ToList();
                
                InvoiceStatusStats.ItemsSource = invoiceStatsDisplay;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحميل إحصائيات المشاريع والفواتير: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل ملخص المشاريع
        /// </summary>
        private async Task LoadProjectSummariesAsync()
        {
            try
            {
                var projectSummaries = await _dataService.GetProjectSummariesAsync();
                ProjectSummaryDataGrid.ItemsSource = projectSummaries;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحميل ملخص المشاريع: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على الاسم المعروض للحالة
        /// </summary>
        private string GetStatusDisplayName(string status)
        {
            return status switch
            {
                "Active" => "نشط",
                "Completed" => "مكتمل",
                "Cancelled" => "ملغي",
                "Pending" => "في الانتظار",
                "Paid" => "مدفوعة",
                "Overdue" => "متأخرة",
                "Task" => "مهمة",
                "Service" => "خدمة",
                "Other" => "أخرى",
                _ => status
            };
        }

        /// <summary>
        /// عرض نافذة لوحة المعلومات
        /// </summary>
        public static void ShowDashboard(Window owner, IDataService dataService)
        {
            var dashboard = new DashboardWindow(dataService)
            {
                Owner = owner
            };
            dashboard.Show();
        }
    }

    /// <summary>
    /// فئة مساعدة لعرض الإحصائيات
    /// </summary>
    public class StatisticItem
    {
        public string Status { get; set; } = string.Empty;
        public int Count { get; set; }
    }
}
