﻿<Window x:Class="FinancialTracker.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FinancialTracker"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        mc:Ignorable="d"
        Title="نظام إدارة المشاريع المالية - Financial Tracker"
        Height="900" Width="1400"
        MinHeight="700" MinWidth="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="{StaticResource ArabicFont}"
        Background="{StaticResource BackgroundBrush}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto">

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- شريط العنوان والقائمة -->
            <materialDesign:ColorZone Grid.Row="0"
                                    Mode="PrimaryMid"
                                    Padding="16"
                                    materialDesign:ShadowAssist.ShadowDepth="Depth2">
                <DockPanel>
                    <!-- العنوان -->
                    <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Finance"
                                               Width="32" Height="32"
                                               VerticalAlignment="Center"
                                               Foreground="White"/>
                        <TextBlock Text="نظام إدارة المشاريع المالية"
                                 FontSize="20"
                                 FontWeight="Bold"
                                 VerticalAlignment="Center"
                                 Margin="8,0,0,0"
                                 Foreground="White"/>
                    </StackPanel>

                    <!-- أزرار التنقل -->
                    <StackPanel DockPanel.Dock="Left"
                              Orientation="Horizontal"
                              HorizontalAlignment="Left">
                        <Button x:Name="BtnRefresh"
                              Style="{StaticResource MaterialDesignToolButton}"
                              ToolTip="تحديث البيانات"
                              Margin="4,0"
                              Click="BtnRefresh_Click">
                            <materialDesign:PackIcon Kind="Refresh"
                                                   Width="24" Height="24"
                                                   Foreground="White"/>
                        </Button>

                        <Button x:Name="BtnSettings"
                              Style="{StaticResource MaterialDesignToolButton}"
                              ToolTip="الإعدادات"
                              Margin="4,0"
                              Click="BtnSettings_Click">
                            <materialDesign:PackIcon Kind="Settings"
                                                   Width="24" Height="24"
                                                   Foreground="White"/>
                        </Button>

                        <Button x:Name="BtnAbout"
                              Style="{StaticResource MaterialDesignToolButton}"
                              ToolTip="حول البرنامج"
                              Margin="4,0"
                              Click="BtnAbout_Click">
                            <materialDesign:PackIcon Kind="Information"
                                                   Width="24" Height="24"
                                                   Foreground="White"/>
                        </Button>
                    </StackPanel>
                </DockPanel>
            </materialDesign:ColorZone>

            <!-- المحتوى الرئيسي -->
            <Grid Grid.Row="1" Margin="16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="300"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- الشريط الجانبي -->
                <materialDesign:Card Grid.Column="0"
                                   Style="{StaticResource CardStyle}"
                                   Margin="0,0,8,0">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel>
                            <!-- لوحة المعلومات -->
                            <TextBlock Text="لوحة المعلومات"
                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                     Margin="0,0,0,16"
                                     HorizontalAlignment="Center"/>

                            <!-- إحصائيات سريعة -->
                            <Grid Margin="0,0,0,16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- إجمالي المشاريع -->
                                <Border Grid.Row="0"
                                      Background="{StaticResource PrimaryBrush}"
                                      CornerRadius="8"
                                      Padding="12"
                                      Margin="0,0,0,8">
                                    <StackPanel>
                                        <TextBlock Text="إجمالي المشاريع"
                                                 Foreground="White"
                                                 FontWeight="Bold"/>
                                        <TextBlock x:Name="TxtTotalProjects"
                                                 Text="0"
                                                 Foreground="White"
                                                 FontSize="24"
                                                 FontWeight="Bold"
                                                 HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- إجمالي الفواتير -->
                                <Border Grid.Row="1"
                                      Background="{StaticResource AccentBrush}"
                                      CornerRadius="8"
                                      Padding="12"
                                      Margin="0,0,0,8">
                                    <StackPanel>
                                        <TextBlock Text="إجمالي مبلغ الفواتير"
                                                 Foreground="White"
                                                 FontWeight="Bold"/>
                                        <TextBlock x:Name="TxtTotalInvoiceAmount"
                                                 Text="0 ج.م"
                                                 Foreground="White"
                                                 FontSize="18"
                                                 FontWeight="Bold"
                                                 HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- المبلغ المدفوع -->
                                <Border Grid.Row="2"
                                      Background="{StaticResource SuccessBrush}"
                                      CornerRadius="8"
                                      Padding="12"
                                      Margin="0,0,0,8">
                                    <StackPanel>
                                        <TextBlock Text="المبلغ المدفوع"
                                                 Foreground="White"
                                                 FontWeight="Bold"/>
                                        <TextBlock x:Name="TxtTotalPaidAmount"
                                                 Text="0 ج.م"
                                                 Foreground="White"
                                                 FontSize="18"
                                                 FontWeight="Bold"
                                                 HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- المبلغ المستحق -->
                                <Border Grid.Row="3"
                                      Background="{StaticResource WarningBrush}"
                                      CornerRadius="8"
                                      Padding="12">
                                    <StackPanel>
                                        <TextBlock Text="المبلغ المستحق"
                                                 Foreground="White"
                                                 FontWeight="Bold"/>
                                        <TextBlock x:Name="TxtTotalOutstandingAmount"
                                                 Text="0 ج.م"
                                                 Foreground="White"
                                                 FontSize="18"
                                                 FontWeight="Bold"
                                                 HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                            </Grid>

                            <Separator Margin="0,8"/>

                            <!-- أزرار الإجراءات السريعة -->
                            <TextBlock Text="الإجراءات السريعة"
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     Margin="0,16,0,8"
                                     HorizontalAlignment="Center"/>

                            <StackPanel>
                                <Button x:Name="BtnAddProject"
                                      Content="إضافة مشروع جديد"
                                      Style="{StaticResource ArabicButtonStyle}"
                                      Background="{StaticResource PrimaryBrush}"
                                      Margin="0,4"
                                      Click="BtnAddProject_Click">
                                    <Button.ContentTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Plus"
                                                                       VerticalAlignment="Center"
                                                                       Margin="0,0,8,0"/>
                                                <TextBlock Text="{Binding}"
                                                         VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </Button.ContentTemplate>
                                </Button>

                                <Button x:Name="BtnAddInvoice"
                                      Content="إضافة فاتورة جديدة"
                                      Style="{StaticResource ArabicButtonStyle}"
                                      Background="{StaticResource AccentBrush}"
                                      Margin="0,4"
                                      Click="BtnAddInvoice_Click">
                                    <Button.ContentTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="FileDocument"
                                                                       VerticalAlignment="Center"
                                                                       Margin="0,0,8,0"/>
                                                <TextBlock Text="{Binding}"
                                                         VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </Button.ContentTemplate>
                                </Button>

                                <Button x:Name="BtnAddCommitment"
                                      Content="إضافة ارتباط جديد"
                                      Style="{StaticResource ArabicButtonStyle}"
                                      Background="{StaticResource SecondaryBrush}"
                                      Margin="0,4"
                                      Click="BtnAddCommitment_Click">
                                    <Button.ContentTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Link"
                                                                       VerticalAlignment="Center"
                                                                       Margin="0,0,8,0"/>
                                                <TextBlock Text="{Binding}"
                                                         VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </Button.ContentTemplate>
                                </Button>

                                <Button x:Name="BtnDashboard"
                                      Content="لوحة المعلومات التفصيلية"
                                      Style="{StaticResource ArabicButtonStyle}"
                                      Background="{StaticResource InfoBrush}"
                                      Margin="0,4"
                                      Click="BtnDashboard_Click">
                                    <Button.ContentTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="ChartLine"
                                                                       VerticalAlignment="Center"
                                                                       Margin="0,0,8,0"/>
                                                <TextBlock Text="{Binding}"
                                                         VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </Button.ContentTemplate>
                                </Button>
                            </StackPanel>
                        </StackPanel>
                    </ScrollViewer>
                </materialDesign:Card>

                <!-- المنطقة الرئيسية -->
                <materialDesign:Card Grid.Column="1"
                                   Style="{StaticResource CardStyle}"
                                   Margin="8,0,0,0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- عنوان القسم -->
                        <StackPanel Grid.Row="0"
                                  Orientation="Horizontal"
                                  Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="ViewList"
                                                   Width="24" Height="24"
                                                   VerticalAlignment="Center"
                                                   Foreground="{StaticResource PrimaryBrush}"/>
                            <TextBlock Text="قائمة المشاريع"
                                     Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                     VerticalAlignment="Center"
                                     Margin="8,0,0,0"/>
                        </StackPanel>

                        <!-- شريط البحث والفلترة -->
                        <Grid Grid.Row="1" Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- مربع البحث -->
                            <TextBox x:Name="TxtSearch"
                                   Grid.Column="0"
                                   Style="{StaticResource ArabicTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="البحث في المشاريع..."
                                   materialDesign:TextFieldAssist.HasLeadingIcon="True"
                                   materialDesign:TextFieldAssist.LeadingIcon="Search"
                                   TextChanged="TxtSearch_TextChanged"
                                   Margin="0,0,8,0"/>

                            <!-- زر البحث -->
                            <Button x:Name="BtnSearch"
                                  Grid.Column="1"
                                  Style="{StaticResource MaterialDesignIconButton}"
                                  ToolTip="بحث"
                                  Click="BtnSearch_Click"
                                  Margin="4,0">
                                <materialDesign:PackIcon Kind="Search"
                                                       Width="20" Height="20"/>
                            </Button>

                            <!-- زر مسح البحث -->
                            <Button x:Name="BtnClearSearch"
                                  Grid.Column="2"
                                  Style="{StaticResource MaterialDesignIconButton}"
                                  ToolTip="مسح البحث"
                                  Click="BtnClearSearch_Click"
                                  Margin="4,0">
                                <materialDesign:PackIcon Kind="Close"
                                                       Width="20" Height="20"/>
                            </Button>
                        </Grid>

                        <!-- جدول المشاريع -->
                        <DataGrid x:Name="ProjectsDataGrid"
                                Grid.Row="2"
                                Style="{StaticResource MaterialDesignDataGrid}"
                                AutoGenerateColumns="False"
                                CanUserAddRows="False"
                                CanUserDeleteRows="False"
                                IsReadOnly="True"
                                SelectionMode="Single"
                                FlowDirection="RightToLeft"
                                FontFamily="{StaticResource ArabicFont}"
                                MouseDoubleClick="ProjectsDataGrid_MouseDoubleClick"
                                SelectionChanged="ProjectsDataGrid_SelectionChanged">

                            <DataGrid.Columns>
                                <!-- معرف المشروع -->
                                <DataGridTextColumn Header="المعرف"
                                                  Binding="{Binding Id}"
                                                  Width="80"
                                                  ElementStyle="{StaticResource ArabicTextStyle}"/>

                                <!-- اسم المشروع -->
                                <DataGridTextColumn Header="اسم المشروع"
                                                  Binding="{Binding Name}"
                                                  Width="200"
                                                  ElementStyle="{StaticResource ArabicTextStyle}"/>

                                <!-- الوصف -->
                                <DataGridTextColumn Header="الوصف"
                                                  Binding="{Binding Description}"
                                                  Width="250"
                                                  ElementStyle="{StaticResource ArabicTextStyle}"/>

                                <!-- الحالة -->
                                <DataGridTemplateColumn Header="الحالة" Width="100">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Border CornerRadius="12"
                                                  Padding="8,4"
                                                  HorizontalAlignment="Center">
                                                <Border.Style>
                                                    <Style TargetType="Border">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding Status}" Value="Active">
                                                                <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="Completed">
                                                                <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="Cancelled">
                                                                <Setter Property="Background" Value="{StaticResource ErrorBrush}"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Border.Style>
                                                <TextBlock Text="{Binding Status}"
                                                         Foreground="White"
                                                         FontWeight="Bold"
                                                         FontSize="11"/>
                                            </Border>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!-- تاريخ الإنشاء -->
                                <DataGridTextColumn Header="تاريخ الإنشاء"
                                                  Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy}"
                                                  Width="120"
                                                  ElementStyle="{StaticResource ArabicTextStyle}"/>

                                <!-- إجمالي الفواتير -->
                                <DataGridTextColumn Header="إجمالي الفواتير"
                                                  Binding="{Binding TotalInvoiceAmount, StringFormat=N2}"
                                                  Width="120"
                                                  ElementStyle="{StaticResource ArabicTextStyle}"/>

                                <!-- المبلغ المدفوع -->
                                <DataGridTextColumn Header="المبلغ المدفوع"
                                                  Binding="{Binding TotalPaidAmount, StringFormat=N2}"
                                                  Width="120"
                                                  ElementStyle="{StaticResource ArabicTextStyle}"/>

                                <!-- المبلغ المستحق -->
                                <DataGridTextColumn Header="المبلغ المستحق"
                                                  Binding="{Binding OutstandingAmount, StringFormat=N2}"
                                                  Width="120"
                                                  ElementStyle="{StaticResource ArabicTextStyle}"/>

                                <!-- الإجراءات -->
                                <DataGridTemplateColumn Header="الإجراءات" Width="150">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal"
                                                      HorizontalAlignment="Center">
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                      ToolTip="عرض التفاصيل"
                                                      Click="BtnViewProject_Click"
                                                      Tag="{Binding Id}"
                                                      Margin="2">
                                                    <materialDesign:PackIcon Kind="Eye"
                                                                           Width="16" Height="16"/>
                                                </Button>

                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                      ToolTip="تعديل"
                                                      Click="BtnEditProject_Click"
                                                      Tag="{Binding Id}"
                                                      Margin="2">
                                                    <materialDesign:PackIcon Kind="Edit"
                                                                           Width="16" Height="16"/>
                                                </Button>

                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                      ToolTip="حذف"
                                                      Click="BtnDeleteProject_Click"
                                                      Tag="{Binding Id}"
                                                      Margin="2">
                                                    <materialDesign:PackIcon Kind="Delete"
                                                                           Width="16" Height="16"
                                                                           Foreground="{StaticResource ErrorBrush}"/>
                                                </Button>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </materialDesign:Card>
            </Grid>

            <!-- شريط الحالة -->
            <materialDesign:ColorZone Grid.Row="2"
                                    Mode="PrimaryDark"
                                    Padding="16,8">
                <DockPanel>
                    <TextBlock x:Name="TxtStatusMessage"
                             Text="جاهز"
                             DockPanel.Dock="Right"
                             VerticalAlignment="Center"
                             Foreground="White"/>

                    <TextBlock x:Name="TxtDateTime"
                             Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat=dd/MM/yyyy HH:mm}"
                             DockPanel.Dock="Left"
                             VerticalAlignment="Center"
                             Foreground="White"/>
                </DockPanel>
            </materialDesign:ColorZone>
        </Grid>
    </materialDesign:DialogHost>
</Window>
