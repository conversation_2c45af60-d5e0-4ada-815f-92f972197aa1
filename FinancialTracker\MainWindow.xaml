﻿<Window x:Class="FinancialTracker.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FinancialTracker"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        mc:Ignorable="d"
        Title="Financial Tracker - Project Management System"
        Height="900" Width="1400"
        MinHeight="700" MinWidth="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="LeftToRight"
        FontFamily="{StaticResource AppFont}"
        Background="{StaticResource BackgroundBrush}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto">

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- شريط العنوان والقائمة -->
            <materialDesign:ColorZone Grid.Row="0"
                                    Mode="PrimaryMid"
                                    Padding="16"
                                    materialDesign:ShadowAssist.ShadowDepth="Depth2">
                <DockPanel>
                    <!-- Title -->
                    <StackPanel DockPanel.Dock="Left" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Finance"
                                               Width="32" Height="32"
                                               VerticalAlignment="Center"
                                               Foreground="White"/>
                        <TextBlock Text="Financial Tracker - Project Management System"
                                 FontSize="20"
                                 FontWeight="Bold"
                                 VerticalAlignment="Center"
                                 Margin="8,0,0,0"
                                 Foreground="White"/>
                    </StackPanel>

                    <!-- Navigation Buttons -->
                    <StackPanel DockPanel.Dock="Right"
                              Orientation="Horizontal"
                              HorizontalAlignment="Right">
                        <Button x:Name="BtnRefresh"
                              Style="{StaticResource MaterialDesignToolButton}"
                              ToolTip="Refresh Data"
                              Margin="4,0"
                              Click="BtnRefresh_Click">
                            <materialDesign:PackIcon Kind="Refresh"
                                                   Width="24" Height="24"
                                                   Foreground="White"/>
                        </Button>

                        <Button x:Name="BtnSettings"
                              Style="{StaticResource MaterialDesignToolButton}"
                              ToolTip="Settings"
                              Margin="4,0"
                              Click="BtnSettings_Click">
                            <materialDesign:PackIcon Kind="Settings"
                                                   Width="24" Height="24"
                                                   Foreground="White"/>
                        </Button>

                        <Button x:Name="BtnAbout"
                              Style="{StaticResource MaterialDesignToolButton}"
                              ToolTip="About"
                              Margin="4,0"
                              Click="BtnAbout_Click">
                            <materialDesign:PackIcon Kind="Information"
                                                   Width="24" Height="24"
                                                   Foreground="White"/>
                        </Button>
                    </StackPanel>
                </DockPanel>
            </materialDesign:ColorZone>

            <!-- المحتوى الرئيسي -->
            <Grid Grid.Row="1" Margin="16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="300"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Sidebar -->
                <materialDesign:Card Grid.Column="0"
                                   Style="{StaticResource CardStyle}"
                                   Margin="0,0,8,0">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel>
                            <!-- Dashboard -->
                            <TextBlock Text="Dashboard"
                                     Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                     Margin="0,0,0,16"
                                     HorizontalAlignment="Center"/>

                            <!-- Quick Statistics -->
                            <Grid Margin="0,0,0,16">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Total Projects -->
                                <Border Grid.Row="0"
                                      Background="{StaticResource PrimaryBrush}"
                                      CornerRadius="8"
                                      Padding="12"
                                      Margin="0,0,0,8">
                                    <StackPanel>
                                        <TextBlock Text="Total Projects"
                                                 Foreground="White"
                                                 FontWeight="Bold"/>
                                        <TextBlock x:Name="TxtTotalProjects"
                                                 Text="0"
                                                 Foreground="White"
                                                 FontSize="24"
                                                 FontWeight="Bold"
                                                 HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- Total Invoice Amount -->
                                <Border Grid.Row="1"
                                      Background="{StaticResource AccentBrush}"
                                      CornerRadius="8"
                                      Padding="12"
                                      Margin="0,0,0,8">
                                    <StackPanel>
                                        <TextBlock Text="Total Invoice Amount"
                                                 Foreground="White"
                                                 FontWeight="Bold"/>
                                        <TextBlock x:Name="TxtTotalInvoiceAmount"
                                                 Text="$0.00"
                                                 Foreground="White"
                                                 FontSize="18"
                                                 FontWeight="Bold"
                                                 HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- Paid Amount -->
                                <Border Grid.Row="2"
                                      Background="{StaticResource SuccessBrush}"
                                      CornerRadius="8"
                                      Padding="12"
                                      Margin="0,0,0,8">
                                    <StackPanel>
                                        <TextBlock Text="Paid Amount"
                                                 Foreground="White"
                                                 FontWeight="Bold"/>
                                        <TextBlock x:Name="TxtTotalPaidAmount"
                                                 Text="$0.00"
                                                 Foreground="White"
                                                 FontSize="18"
                                                 FontWeight="Bold"
                                                 HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- Outstanding Amount -->
                                <Border Grid.Row="3"
                                      Background="{StaticResource WarningBrush}"
                                      CornerRadius="8"
                                      Padding="12">
                                    <StackPanel>
                                        <TextBlock Text="Outstanding Amount"
                                                 Foreground="White"
                                                 FontWeight="Bold"/>
                                        <TextBlock x:Name="TxtTotalOutstandingAmount"
                                                 Text="$0.00"
                                                 Foreground="White"
                                                 FontSize="18"
                                                 FontWeight="Bold"
                                                 HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                            </Grid>

                            <Separator Margin="0,8"/>

                            <!-- Quick Actions -->
                            <TextBlock Text="Quick Actions"
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     Margin="0,16,0,8"
                                     HorizontalAlignment="Center"/>

                            <StackPanel>
                                <Button x:Name="BtnAddProject"
                                      Content="Add New Project"
                                      Style="{StaticResource AppButtonStyle}"
                                      Background="{StaticResource PrimaryBrush}"
                                      Margin="0,4"
                                      Click="BtnAddProject_Click">
                                    <Button.ContentTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Plus"
                                                                       VerticalAlignment="Center"
                                                                       Margin="0,0,8,0"/>
                                                <TextBlock Text="{Binding}"
                                                         VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </Button.ContentTemplate>
                                </Button>

                                <Button x:Name="BtnAddInvoice"
                                      Content="Add New Invoice"
                                      Style="{StaticResource AppButtonStyle}"
                                      Background="{StaticResource AccentBrush}"
                                      Margin="0,4"
                                      Click="BtnAddInvoice_Click">
                                    <Button.ContentTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="FileDocument"
                                                                       VerticalAlignment="Center"
                                                                       Margin="0,0,8,0"/>
                                                <TextBlock Text="{Binding}"
                                                         VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </Button.ContentTemplate>
                                </Button>

                                <Button x:Name="BtnAddCommitment"
                                      Content="Add New Commitment"
                                      Style="{StaticResource AppButtonStyle}"
                                      Background="{StaticResource SecondaryBrush}"
                                      Margin="0,4"
                                      Click="BtnAddCommitment_Click">
                                    <Button.ContentTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="Link"
                                                                       VerticalAlignment="Center"
                                                                       Margin="0,0,8,0"/>
                                                <TextBlock Text="{Binding}"
                                                         VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </Button.ContentTemplate>
                                </Button>

                                <Button x:Name="BtnDashboard"
                                      Content="Detailed Dashboard"
                                      Style="{StaticResource AppButtonStyle}"
                                      Background="{StaticResource InfoBrush}"
                                      Margin="0,4"
                                      Click="BtnDashboard_Click">
                                    <Button.ContentTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="ChartLine"
                                                                       VerticalAlignment="Center"
                                                                       Margin="0,0,8,0"/>
                                                <TextBlock Text="{Binding}"
                                                         VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </Button.ContentTemplate>
                                </Button>
                            </StackPanel>
                        </StackPanel>
                    </ScrollViewer>
                </materialDesign:Card>

                <!-- Main Area -->
                <materialDesign:Card Grid.Column="1"
                                   Style="{StaticResource CardStyle}"
                                   Margin="8,0,0,0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Section Title -->
                        <StackPanel Grid.Row="0"
                                  Orientation="Horizontal"
                                  Margin="0,0,0,16">
                            <materialDesign:PackIcon Kind="ViewList"
                                                   Width="24" Height="24"
                                                   VerticalAlignment="Center"
                                                   Foreground="{StaticResource PrimaryBrush}"/>
                            <TextBlock Text="Projects List"
                                     Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                     VerticalAlignment="Center"
                                     Margin="8,0,0,0"/>
                        </StackPanel>

                        <!-- Search and Filter Bar -->
                        <Grid Grid.Row="1" Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- Search Box -->
                            <TextBox x:Name="TxtSearch"
                                   Grid.Column="0"
                                   Style="{StaticResource AppTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="Search projects..."
                                   materialDesign:TextFieldAssist.HasLeadingIcon="True"
                                   materialDesign:TextFieldAssist.LeadingIcon="Search"
                                   TextChanged="TxtSearch_TextChanged"
                                   Margin="0,0,8,0"/>

                            <!-- Search Button -->
                            <Button x:Name="BtnSearch"
                                  Grid.Column="1"
                                  Style="{StaticResource MaterialDesignIconButton}"
                                  ToolTip="Search"
                                  Click="BtnSearch_Click"
                                  Margin="4,0">
                                <materialDesign:PackIcon Kind="Search"
                                                       Width="20" Height="20"/>
                            </Button>

                            <!-- Clear Search Button -->
                            <Button x:Name="BtnClearSearch"
                                  Grid.Column="2"
                                  Style="{StaticResource MaterialDesignIconButton}"
                                  ToolTip="Clear Search"
                                  Click="BtnClearSearch_Click"
                                  Margin="4,0">
                                <materialDesign:PackIcon Kind="Close"
                                                       Width="20" Height="20"/>
                            </Button>
                        </Grid>

                        <!-- Projects Table -->
                        <DataGrid x:Name="ProjectsDataGrid"
                                Grid.Row="2"
                                Style="{StaticResource MaterialDesignDataGrid}"
                                AutoGenerateColumns="False"
                                CanUserAddRows="False"
                                CanUserDeleteRows="False"
                                IsReadOnly="True"
                                SelectionMode="Single"
                                FlowDirection="LeftToRight"
                                FontFamily="{StaticResource AppFont}"
                                MouseDoubleClick="ProjectsDataGrid_MouseDoubleClick"
                                SelectionChanged="ProjectsDataGrid_SelectionChanged">

                            <DataGrid.Columns>
                                <!-- Project ID -->
                                <DataGridTextColumn Header="ID"
                                                  Binding="{Binding Id}"
                                                  Width="80"
                                                  ElementStyle="{StaticResource AppTextStyle}"/>

                                <!-- Project Name -->
                                <DataGridTextColumn Header="Project Name"
                                                  Binding="{Binding Name}"
                                                  Width="200"
                                                  ElementStyle="{StaticResource AppTextStyle}"/>

                                <!-- Description -->
                                <DataGridTextColumn Header="Description"
                                                  Binding="{Binding Description}"
                                                  Width="250"
                                                  ElementStyle="{StaticResource AppTextStyle}"/>

                                <!-- Status -->
                                <DataGridTemplateColumn Header="Status" Width="100">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Border CornerRadius="12"
                                                  Padding="8,4"
                                                  HorizontalAlignment="Center">
                                                <Border.Style>
                                                    <Style TargetType="Border">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding Status}" Value="Active">
                                                                <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="Completed">
                                                                <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding Status}" Value="Cancelled">
                                                                <Setter Property="Background" Value="{StaticResource ErrorBrush}"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Border.Style>
                                                <TextBlock Text="{Binding Status}"
                                                         Foreground="White"
                                                         FontWeight="Bold"
                                                         FontSize="11"/>
                                            </Border>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!-- Created Date -->
                                <DataGridTextColumn Header="Created Date"
                                                  Binding="{Binding CreatedDate, StringFormat=MM/dd/yyyy}"
                                                  Width="120"
                                                  ElementStyle="{StaticResource AppTextStyle}"/>

                                <!-- Total Invoice Amount -->
                                <DataGridTextColumn Header="Total Invoices"
                                                  Binding="{Binding TotalInvoiceAmount, StringFormat=C}"
                                                  Width="120"
                                                  ElementStyle="{StaticResource AppTextStyle}"/>

                                <!-- Paid Amount -->
                                <DataGridTextColumn Header="Paid Amount"
                                                  Binding="{Binding TotalPaidAmount, StringFormat=C}"
                                                  Width="120"
                                                  ElementStyle="{StaticResource AppTextStyle}"/>

                                <!-- Outstanding Amount -->
                                <DataGridTextColumn Header="Outstanding"
                                                  Binding="{Binding OutstandingAmount, StringFormat=C}"
                                                  Width="120"
                                                  ElementStyle="{StaticResource AppTextStyle}"/>

                                <!-- Actions -->
                                <DataGridTemplateColumn Header="Actions" Width="150">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal"
                                                      HorizontalAlignment="Center">
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                      ToolTip="View Details"
                                                      Click="BtnViewProject_Click"
                                                      Tag="{Binding Id}"
                                                      Margin="2">
                                                    <materialDesign:PackIcon Kind="Eye"
                                                                           Width="16" Height="16"/>
                                                </Button>

                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                      ToolTip="Edit"
                                                      Click="BtnEditProject_Click"
                                                      Tag="{Binding Id}"
                                                      Margin="2">
                                                    <materialDesign:PackIcon Kind="Edit"
                                                                           Width="16" Height="16"/>
                                                </Button>

                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                      ToolTip="Delete"
                                                      Click="BtnDeleteProject_Click"
                                                      Tag="{Binding Id}"
                                                      Margin="2">
                                                    <materialDesign:PackIcon Kind="Delete"
                                                                           Width="16" Height="16"
                                                                           Foreground="{StaticResource ErrorBrush}"/>
                                                </Button>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </materialDesign:Card>
            </Grid>

            <!-- Status Bar -->
            <materialDesign:ColorZone Grid.Row="2"
                                    Mode="PrimaryDark"
                                    Padding="16,8">
                <DockPanel>
                    <TextBlock x:Name="TxtStatusMessage"
                             Text="Ready"
                             DockPanel.Dock="Left"
                             VerticalAlignment="Center"
                             Foreground="White"/>

                    <TextBlock x:Name="TxtDateTime"
                             Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat=MM/dd/yyyy HH:mm}"
                             DockPanel.Dock="Right"
                             VerticalAlignment="Center"
                             Foreground="White"/>
                </DockPanel>
            </materialDesign:ColorZone>
        </Grid>
    </materialDesign:DialogHost>
</Window>
