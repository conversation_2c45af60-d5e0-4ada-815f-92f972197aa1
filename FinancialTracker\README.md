# Financial Tracker - Project Management System

## Overview
A comprehensive financial project management system developed using .NET 6 and WPF with Material Design.

## Key Features

### 🏗️ Project Management
- Create, edit, and delete projects
- Track project status (Active, Completed, Cancelled)
- Manage planned and actual budgets
- Track completion percentage
- Link dates (start and end)

### 📄 Invoice Management
- Create and edit invoices
- Link invoices to projects and commitments
- Track paid and outstanding amounts
- Manage issue and due dates
- Categorize invoices (Task, Service, Other)
- Track invoice status (Pending, Paid, Overdue, Cancelled)

### 🔗 Commitment Management
- Create and manage financial commitments
- Link commitments to projects
- Track completion percentage
- Manage attached files

### 💬 Reply System
- Manage replies to invoices and commitments
- Categorize replies (Approval, Rejection, Modification, Information)
- Track priority and status
- Manage sender and receiver

### 📊 Dashboard and Reports
- Comprehensive statistics for projects and invoices
- Detailed financial reports
- Charts for statuses and types
- Project summary with statistics

### 📁 File Management
- Upload and manage related files
- Organize files by categories
- Open files with default application
- File backups

## Technologies Used

### Framework and Platform
- **.NET 6** - Core framework
- **WPF (Windows Presentation Foundation)** - User interface
- **C#** - Programming language

### Database
- **SQLite** - Local database
- **Entity Framework Core 6.0** - ORM for database interaction
- **Code First Migrations** - Database structure management

### User Interface
- **Material Design in XAML** - Material Design styling
- **Responsive Design** - Adapts to different screen sizes
- **Modern UI Components** - Clean and intuitive interface

### Dependency Management
- **Microsoft.Extensions.DependencyInjection** - Dependency injection
- **MVVM Pattern** - Model-View-ViewModel design pattern

## Project Structure

```
FinancialTracker/
├── Models/                 # Data models
│   ├── Project.cs         # Project model
│   ├── Invoice.cs         # Invoice model
│   ├── Commitment.cs      # Commitment model
│   ├── Reply.cs           # Reply model
│   └── ProjectSummary.cs  # Project summary model
├── Data/                   # Data layer
│   ├── FinancialContext.cs # Database context
│   ├── DesignTimeDbContextFactory.cs
│   └── SampleDataSeeder.cs # Sample data seeder
├── Services/               # Service layer
│   ├── IDataService.cs    # Data service interface
│   ├── DataService.cs     # Data service implementation
│   ├── IFileService.cs    # File service interface
│   └── FileService.cs     # File service implementation
├── Dialogs/               # Dialog windows
│   ├── ProjectDialog.xaml # Project management dialog
│   ├── InvoiceDialog.xaml # Invoice management dialog
│   └── CommitmentDialog.xaml # Commitment management dialog
├── Views/                 # Main windows
│   └── DashboardWindow.xaml # Dashboard window
├── MainWindow.xaml        # Main window
└── App.xaml              # Main WPF application
```

## System Requirements

### Runtime Requirements
- **Windows 10** or newer
- **.NET 6 Runtime** or newer

### Development Requirements
- **Visual Studio 2022** or **Visual Studio Code**
- **.NET 6 SDK**
- **Git** (optional)

## Installation and Running

### 1. Clone the Project
```bash
git clone [repository-url]
cd FinancialTracker
```

### 2. Restore Packages
```bash
dotnet restore
```

### 3. Build the Project
```bash
dotnet build
```

### 4. Run the Application
```bash
dotnet run
```

## Sample Data

The project includes sample data for testing:
- **10 diverse projects** (UMS, BNG, AAA, NTP, HPBX, etc.)
- **12+ commitments** linked to projects
- **25+ invoices** with different statuses
- **16 replies** to invoices and commitments

## Usage

### Project Management
1. Click "Add New Project" in the sidebar
2. Fill in the required data (name, description, budget, etc.)
3. Save the project

### Invoice Management
1. Click "Add New Invoice"
2. Select the related project or commitment
3. Enter amount and issue/due dates
4. Attach invoice file (optional)

### View Reports
1. Click "Detailed Dashboard"
2. Browse statistics and financial reports
3. Review project summaries

## Contributing

We welcome contributions! Please:
1. Fork the project
2. Create a new feature branch
3. Apply changes with tests
4. Submit a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support or to report issues:
- Open a new Issue on GitHub
- Contact us via email

## Future Releases

### Planned Features
- [ ] Export reports to PDF/Excel
- [ ] Notification system for overdue invoices
- [ ] Web interface for remote access
- [ ] Integration with external accounting systems
- [ ] Advanced permissions system
- [ ] Advanced financial reports with charts

---

**Developed with ❤️ using .NET 6 and WPF**
