# نظام إدارة المشاريع المالية - Financial Tracker

## نظرة عامة
نظام شامل لإدارة المشاريع المالية مطور باستخدام .NET 6 و WPF مع Material Design ودعم كامل للغة العربية.

## المميزات الرئيسية

### 🏗️ إدارة المشاريع
- إنشاء وتعديل وحذف المشاريع
- تتبع حالة المشروع (نشط، مكتمل، ملغي)
- إدارة الميزانية المخططة والفعلية
- تتبع نسبة الإنجاز
- ربط التواريخ (البداية والنهاية)

### 📄 إدارة الفواتير
- إنشاء وتعديل الفواتير
- ربط الفواتير بالمشاريع والارتباطات
- تتبع المبالغ المدفوعة والمستحقة
- إدارة تواريخ الإصدار والاستحقاق
- تصنيف الفواتير (مهمة، خدمة، أخرى)
- تتبع حالة الفاتورة (في الانتظار، مدفوعة، متأخرة، ملغية)

### 🔗 إدارة الارتباطات
- إنشاء وإدارة الارتباطات المالية
- ربط الارتباطات بالمشاريع
- تتبع نسبة الإنجاز
- إدارة الملفات المرفقة

### 💬 نظام الردود
- إدارة الردود على الفواتير والارتباطات
- تصنيف الردود (موافقة، رفض، تعديل، معلومات)
- تتبع الأولوية والحالة
- إدارة المرسل والمستقبل

### 📊 لوحة المعلومات والتقارير
- إحصائيات شاملة للمشاريع والفواتير
- تقارير مالية تفصيلية
- رسوم بيانية للحالات والأنواع
- ملخص المشاريع مع الإحصائيات

### 📁 إدارة الملفات
- رفع وإدارة الملفات المرتبطة
- تنظيم الملفات حسب الفئات
- فتح الملفات بالتطبيق الافتراضي
- نسخ احتياطية للملفات

## التقنيات المستخدمة

### الإطار والمنصة
- **.NET 6** - إطار العمل الأساسي
- **WPF (Windows Presentation Foundation)** - واجهة المستخدم
- **C#** - لغة البرمجة

### قاعدة البيانات
- **SQLite** - قاعدة بيانات محلية
- **Entity Framework Core 6.0** - ORM للتعامل مع قاعدة البيانات
- **Code First Migrations** - إدارة هيكل قاعدة البيانات

### واجهة المستخدم
- **Material Design in XAML** - تصميم Material Design
- **دعم كامل للغة العربية** - RTL وخطوط عربية
- **تصميم متجاوب** - يتكيف مع أحجام الشاشات المختلفة

### إدارة التبعيات
- **Microsoft.Extensions.DependencyInjection** - حقن التبعيات
- **MVVM Pattern** - نمط تصميم Model-View-ViewModel

## هيكل المشروع

```
FinancialTracker/
├── Models/                 # نماذج البيانات
│   ├── Project.cs         # نموذج المشروع
│   ├── Invoice.cs         # نموذج الفاتورة
│   ├── Commitment.cs      # نموذج الارتباط
│   ├── Reply.cs           # نموذج الرد
│   └── ProjectSummary.cs  # نموذج ملخص المشروع
├── Data/                   # طبقة البيانات
│   ├── FinancialContext.cs # سياق قاعدة البيانات
│   ├── DesignTimeDbContextFactory.cs
│   └── SampleDataSeeder.cs # البيانات الأولية
├── Services/               # طبقة الخدمات
│   ├── IDataService.cs    # واجهة خدمة البيانات
│   ├── DataService.cs     # تطبيق خدمة البيانات
│   ├── IFileService.cs    # واجهة خدمة الملفات
│   └── FileService.cs     # تطبيق خدمة الملفات
├── Dialogs/               # نوافذ الحوار
│   ├── ProjectDialog.xaml # نافذة إدارة المشروع
│   ├── InvoiceDialog.xaml # نافذة إدارة الفاتورة
│   └── CommitmentDialog.xaml # نافذة إدارة الارتباط
├── Views/                 # النوافذ الرئيسية
│   └── DashboardWindow.xaml # نافذة لوحة المعلومات
├── MainWindow.xaml        # النافذة الرئيسية
└── App.xaml              # تطبيق WPF الرئيسي
```

## متطلبات التشغيل

### متطلبات النظام
- **Windows 10** أو أحدث
- **.NET 6 Runtime** أو أحدث

### متطلبات التطوير
- **Visual Studio 2022** أو **Visual Studio Code**
- **.NET 6 SDK**
- **Git** (اختياري)

## التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone [repository-url]
cd FinancialTracker
```

### 2. استعادة الحزم
```bash
dotnet restore
```

### 3. بناء المشروع
```bash
dotnet build
```

### 4. تشغيل التطبيق
```bash
dotnet run
```

## البيانات الأولية

يحتوي المشروع على بيانات أولية تجريبية تشمل:
- **5 مشاريع** متنوعة (UMS, BNG, AAA, NTP, HPBX)
- **12 ارتباط** مرتبط بالمشاريع
- **25+ فاتورة** مع حالات مختلفة
- **16 رد** على الفواتير والارتباطات

## الاستخدام

### إدارة المشاريع
1. انقر على "إضافة مشروع جديد" في الشريط الجانبي
2. املأ البيانات المطلوبة (الاسم، الوصف، الميزانية، إلخ)
3. احفظ المشروع

### إدارة الفواتير
1. انقر على "إضافة فاتورة جديدة"
2. اختر المشروع أو الارتباط المرتبط
3. أدخل المبلغ وتواريخ الإصدار والاستحقاق
4. أرفق ملف الفاتورة (اختياري)

### عرض التقارير
1. انقر على "لوحة المعلومات التفصيلية"
2. استعرض الإحصائيات والتقارير المالية
3. راجع ملخص المشاريع

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. تطبيق التغييرات مع الاختبارات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- افتح Issue جديد في GitHub
- راسلنا على البريد الإلكتروني

## الإصدارات المستقبلية

### المميزات المخططة
- [ ] تصدير التقارير إلى PDF/Excel
- [ ] نظام إشعارات للفواتير المتأخرة
- [ ] واجهة ويب للوصول عن بُعد
- [ ] تكامل مع أنظمة المحاسبة الخارجية
- [ ] نظام صلاحيات متقدم
- [ ] تقارير مالية متقدمة مع رسوم بيانية

---

**تم التطوير بـ ❤️ باستخدام .NET 6 و WPF**
