using Microsoft.EntityFrameworkCore;
using FinancialTracker.Models;
using System;
using System.IO;

namespace FinancialTracker.Data
{
    /// <summary>
    /// سياق قاعدة البيانات الرئيسي للنظام المالي
    /// </summary>
    public class FinancialContext : DbContext
    {
        /// <summary>
        /// جدول المشاريع
        /// </summary>
        public DbSet<Project> Projects { get; set; }

        /// <summary>
        /// جدول الفواتير
        /// </summary>
        public DbSet<Invoice> Invoices { get; set; }

        /// <summary>
        /// جدول الارتباطات
        /// </summary>
        public DbSet<Commitment> Commitments { get; set; }

        /// <summary>
        /// جدول الردود
        /// </summary>
        public DbSet<Reply> Replies { get; set; }

        /// <summary>
        /// إعداد اتصال قاعدة البيانات
        /// </summary>
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "FinancialTracker.db");
                optionsBuilder.UseSqlite($"Data Source={dbPath}");
            }
        }

        /// <summary>
        /// إعداد نماذج البيانات والعلاقات
        /// </summary>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // إعداد نموذج المشروع
            modelBuilder.Entity<Project>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Status).IsRequired().HasMaxLength(50).HasDefaultValue("Active");
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.CompletionPercentage).HasDefaultValue(0);
                
                // فهرس على اسم المشروع
                entity.HasIndex(e => e.Name);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.CreatedDate);
            });

            // إعداد نموذج الفاتورة
            modelBuilder.Entity<Invoice>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.InvoiceNumber).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.PaidAmount).HasColumnType("decimal(18,2)").HasDefaultValue(0);
                entity.Property(e => e.Type).IsRequired().HasMaxLength(50).HasDefaultValue("Task");
                entity.Property(e => e.Status).HasMaxLength(50).HasDefaultValue("Pending");
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");

                // العلاقة مع المشروع
                entity.HasOne(e => e.Project)
                      .WithMany(p => p.Invoices)
                      .HasForeignKey(e => e.ProjectId)
                      .OnDelete(DeleteBehavior.SetNull);

                // العلاقة مع الارتباط
                entity.HasOne(e => e.Commitment)
                      .WithMany(c => c.Invoices)
                      .HasForeignKey(e => e.CommitmentId)
                      .OnDelete(DeleteBehavior.SetNull);

                // فهارس
                entity.HasIndex(e => e.InvoiceNumber).IsUnique();
                entity.HasIndex(e => e.IssueDate);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Type);
            });

            // إعداد نموذج الارتباط
            modelBuilder.Entity<Commitment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.CommitmentNumber).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Type).IsRequired().HasMaxLength(50).HasDefaultValue("Task");
                entity.Property(e => e.Status).HasMaxLength(50).HasDefaultValue("Active");
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.CompletionPercentage).HasDefaultValue(0);

                // العلاقة مع المشروع
                entity.HasOne(e => e.Project)
                      .WithMany(p => p.Commitments)
                      .HasForeignKey(e => e.ProjectId)
                      .OnDelete(DeleteBehavior.SetNull);

                // فهارس
                entity.HasIndex(e => e.CommitmentNumber).IsUnique();
                entity.HasIndex(e => e.IssueDate);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Type);
            });

            // إعداد نموذج الرد
            modelBuilder.Entity<Reply>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ReplyNumber).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Type).HasMaxLength(50).HasDefaultValue("Information");
                entity.Property(e => e.Status).HasMaxLength(50).HasDefaultValue("Pending");
                entity.Property(e => e.Priority).HasMaxLength(20).HasDefaultValue("Medium");
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.IsResponded).HasDefaultValue(false);

                // العلاقة مع الفاتورة
                entity.HasOne(e => e.Invoice)
                      .WithMany(i => i.Replies)
                      .HasForeignKey(e => e.InvoiceId)
                      .OnDelete(DeleteBehavior.SetNull);

                // العلاقة مع الارتباط
                entity.HasOne(e => e.Commitment)
                      .WithMany(c => c.Replies)
                      .HasForeignKey(e => e.CommitmentId)
                      .OnDelete(DeleteBehavior.SetNull);

                // فهارس
                entity.HasIndex(e => e.ReplyNumber).IsUnique();
                entity.HasIndex(e => e.IssueDate);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.Type);
                entity.HasIndex(e => e.Priority);
            });

            // إضافة البيانات الأولية للمشاريع
            SeedData(modelBuilder);
        }



        /// <summary>
        /// إضافة البيانات الأولية للمشاريع
        /// </summary>
        private void SeedData(ModelBuilder modelBuilder)
        {
            var projects = new[]
            {
                new Project { Id = 1, Name = "UMS", Description = "University Management System", Status = "Active", CreatedDate = DateTime.Now.AddDays(-30), PlannedBudget = 500000 },
                new Project { Id = 2, Name = "BNG", Description = "Banking and Finance System", Status = "Active", CreatedDate = DateTime.Now.AddDays(-25), PlannedBudget = 750000 },
                new Project { Id = 3, Name = "AAA", Description = "Authentication and Authorization", Status = "Completed", CreatedDate = DateTime.Now.AddDays(-60), PlannedBudget = 200000, CompletionPercentage = 100 },
                new Project { Id = 4, Name = "NTP", Description = "Network Time Protocol", Status = "Active", CreatedDate = DateTime.Now.AddDays(-20), PlannedBudget = 150000 },
                new Project { Id = 5, Name = "HPBX", Description = "High Performance Business Exchange", Status = "Active", CreatedDate = DateTime.Now.AddDays(-15), PlannedBudget = 1000000 },
                new Project { Id = 6, Name = "CRM", Description = "Customer Relationship Management", Status = "Active", CreatedDate = DateTime.Now.AddDays(-10), PlannedBudget = 600000 },
                new Project { Id = 7, Name = "ERP", Description = "Enterprise Resource Planning", Status = "Active", CreatedDate = DateTime.Now.AddDays(-5), PlannedBudget = 1200000 },
                new Project { Id = 8, Name = "BI", Description = "Business Intelligence System", Status = "Active", CreatedDate = DateTime.Now.AddDays(-3), PlannedBudget = 400000 },
                new Project { Id = 9, Name = "IOT", Description = "Internet of Things Platform", Status = "Active", CreatedDate = DateTime.Now.AddDays(-2), PlannedBudget = 800000 },
                new Project { Id = 10, Name = "AI", Description = "Artificial Intelligence Framework", Status = "Active", CreatedDate = DateTime.Now.AddDays(-1), PlannedBudget = 900000 }
            };

            modelBuilder.Entity<Project>().HasData(projects);
        }
    }
}
