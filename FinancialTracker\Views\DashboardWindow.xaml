<Window x:Class="FinancialTracker.Views.DashboardWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="لوحة المعلومات - Dashboard" 
        Height="800" Width="1200"
        MinHeight="600" MinWidth="1000"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="{StaticResource ArabicFont}"
        Background="{StaticResource BackgroundBrush}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        ResizeMode="CanResize">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
            <materialDesign:PackIcon Kind="ChartLine" 
                                   Width="32" Height="32"
                                   VerticalAlignment="Center"
                                   Foreground="{StaticResource PrimaryBrush}"/>
            <TextBlock Text="لوحة المعلومات والتقارير المالية" 
                     Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                     VerticalAlignment="Center"
                     Margin="12,0,0,0"/>
        </StackPanel>

        <!-- المحتوى الرئيسي -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- الإحصائيات السريعة -->
                <Grid Grid.Row="0" Margin="0,0,0,24">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- إجمالي المشاريع -->
                    <materialDesign:Card Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,8,0">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                <materialDesign:PackIcon Kind="FolderMultiple" 
                                                       Width="24" Height="24"
                                                       Foreground="{StaticResource PrimaryBrush}"
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="إجمالي المشاريع" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         Margin="8,0,0,0"/>
                            </StackPanel>
                            <TextBlock x:Name="TxtTotalProjects" 
                                     Text="0"
                                     Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                                     Foreground="{StaticResource PrimaryBrush}"
                                     HorizontalAlignment="Center"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- إجمالي الفواتير -->
                    <materialDesign:Card Grid.Column="1" Style="{StaticResource CardStyle}" Margin="8,0">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                <materialDesign:PackIcon Kind="FileDocument" 
                                                       Width="24" Height="24"
                                                       Foreground="{StaticResource AccentBrush}"
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="إجمالي الفواتير" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         Margin="8,0,0,0"/>
                            </StackPanel>
                            <TextBlock x:Name="TxtTotalInvoices" 
                                     Text="0"
                                     Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                                     Foreground="{StaticResource AccentBrush}"
                                     HorizontalAlignment="Center"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- إجمالي الارتباطات -->
                    <materialDesign:Card Grid.Column="2" Style="{StaticResource CardStyle}" Margin="8,0">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                <materialDesign:PackIcon Kind="Link" 
                                                       Width="24" Height="24"
                                                       Foreground="{StaticResource SecondaryBrush}"
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="إجمالي الارتباطات" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         Margin="8,0,0,0"/>
                            </StackPanel>
                            <TextBlock x:Name="TxtTotalCommitments" 
                                     Text="0"
                                     Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                                     Foreground="{StaticResource SecondaryBrush}"
                                     HorizontalAlignment="Center"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- الفواتير المتأخرة -->
                    <materialDesign:Card Grid.Column="3" Style="{StaticResource CardStyle}" Margin="8,0,0,0">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                <materialDesign:PackIcon Kind="AlertCircle" 
                                                       Width="24" Height="24"
                                                       Foreground="{StaticResource ErrorBrush}"
                                                       VerticalAlignment="Center"/>
                                <TextBlock Text="الفواتير المتأخرة" 
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         Margin="8,0,0,0"/>
                            </StackPanel>
                            <TextBlock x:Name="TxtOverdueInvoices" 
                                     Text="0"
                                     Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                                     Foreground="{StaticResource ErrorBrush}"
                                     HorizontalAlignment="Center"/>
                        </StackPanel>
                    </materialDesign:Card>
                </Grid>

                <!-- الإحصائيات المالية -->
                <Grid Grid.Row="1" Margin="0,0,0,24">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- إجمالي المبالغ -->
                    <materialDesign:Card Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,8,0">
                        <StackPanel>
                            <TextBlock Text="إجمالي مبلغ الفواتير" 
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     HorizontalAlignment="Center"
                                     Margin="0,0,0,8"/>
                            <TextBlock x:Name="TxtTotalInvoiceAmount" 
                                     Text="0.00 ج.م"
                                     Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                     Foreground="{StaticResource InfoBrush}"
                                     HorizontalAlignment="Center"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- المبلغ المدفوع -->
                    <materialDesign:Card Grid.Column="1" Style="{StaticResource CardStyle}" Margin="8,0">
                        <StackPanel>
                            <TextBlock Text="المبلغ المدفوع" 
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     HorizontalAlignment="Center"
                                     Margin="0,0,0,8"/>
                            <TextBlock x:Name="TxtTotalPaidAmount" 
                                     Text="0.00 ج.م"
                                     Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                     Foreground="{StaticResource SuccessBrush}"
                                     HorizontalAlignment="Center"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- المبلغ المستحق -->
                    <materialDesign:Card Grid.Column="2" Style="{StaticResource CardStyle}" Margin="8,0,0,0">
                        <StackPanel>
                            <TextBlock Text="المبلغ المستحق" 
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     HorizontalAlignment="Center"
                                     Margin="0,0,0,8"/>
                            <TextBlock x:Name="TxtTotalOutstandingAmount" 
                                     Text="0.00 ج.م"
                                     Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                     Foreground="{StaticResource WarningBrush}"
                                     HorizontalAlignment="Center"/>
                        </StackPanel>
                    </materialDesign:Card>
                </Grid>

                <!-- التقارير والرسوم البيانية -->
                <Grid Grid.Row="2" Margin="0,0,0,24">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- إحصائيات المشاريع حسب الحالة -->
                    <materialDesign:Card Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,8,0">
                        <StackPanel>
                            <TextBlock Text="المشاريع حسب الحالة" 
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     HorizontalAlignment="Center"
                                     Margin="0,0,0,16"/>
                            
                            <ItemsControl x:Name="ProjectStatusStats">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Grid Margin="0,4">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <TextBlock Grid.Column="0" 
                                                     Text="{Binding Status}" 
                                                     VerticalAlignment="Center"/>
                                            
                                            <Border Grid.Column="1" 
                                                  Background="{StaticResource PrimaryBrush}"
                                                  CornerRadius="12"
                                                  Padding="8,4">
                                                <TextBlock Text="{Binding Count}" 
                                                         Foreground="White"
                                                         FontWeight="Bold"/>
                                            </Border>
                                        </Grid>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- إحصائيات الفواتير حسب الحالة -->
                    <materialDesign:Card Grid.Column="1" Style="{StaticResource CardStyle}" Margin="8,0,0,0">
                        <StackPanel>
                            <TextBlock Text="الفواتير حسب الحالة" 
                                     Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                     HorizontalAlignment="Center"
                                     Margin="0,0,0,16"/>
                            
                            <ItemsControl x:Name="InvoiceStatusStats">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Grid Margin="0,4">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <TextBlock Grid.Column="0" 
                                                     Text="{Binding Status}" 
                                                     VerticalAlignment="Center"/>
                                            
                                            <Border Grid.Column="1" 
                                                  Background="{StaticResource AccentBrush}"
                                                  CornerRadius="12"
                                                  Padding="8,4">
                                                <TextBlock Text="{Binding Count}" 
                                                         Foreground="White"
                                                         FontWeight="Bold"/>
                                            </Border>
                                        </Grid>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </materialDesign:Card>
                </Grid>

                <!-- قائمة المشاريع مع الملخصات -->
                <materialDesign:Card Grid.Row="3" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="ملخص المشاريع" 
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 HorizontalAlignment="Center"
                                 Margin="0,0,0,16"/>
                        
                        <DataGrid x:Name="ProjectSummaryDataGrid"
                                Style="{StaticResource MaterialDesignDataGrid}"
                                AutoGenerateColumns="False"
                                CanUserAddRows="False"
                                CanUserDeleteRows="False"
                                IsReadOnly="True"
                                SelectionMode="Single"
                                FlowDirection="RightToLeft"
                                FontFamily="{StaticResource ArabicFont}"
                                MaxHeight="300">
                            
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="اسم المشروع" 
                                                  Binding="{Binding ProjectName}"
                                                  Width="150"/>
                                
                                <DataGridTextColumn Header="الحالة" 
                                                  Binding="{Binding Status}"
                                                  Width="80"/>
                                
                                <DataGridTextColumn Header="عدد الفواتير" 
                                                  Binding="{Binding InvoiceCount}"
                                                  Width="80"/>
                                
                                <DataGridTextColumn Header="إجمالي المبلغ" 
                                                  Binding="{Binding TotalInvoiceAmount, StringFormat=N2}"
                                                  Width="120"/>
                                
                                <DataGridTextColumn Header="المبلغ المدفوع" 
                                                  Binding="{Binding TotalPaidAmount, StringFormat=N2}"
                                                  Width="120"/>
                                
                                <DataGridTextColumn Header="المبلغ المستحق" 
                                                  Binding="{Binding OutstandingAmount, StringFormat=N2}"
                                                  Width="120"/>
                                
                                <DataGridTextColumn Header="نسبة الإنجاز" 
                                                  Binding="{Binding CompletionPercentage, StringFormat=F1}"
                                                  Width="100"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>
        </ScrollViewer>
    </Grid>
</Window>
