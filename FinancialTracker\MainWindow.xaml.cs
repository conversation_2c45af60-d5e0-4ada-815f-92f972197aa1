﻿using FinancialTracker.Models;
using FinancialTracker.Services;
using FinancialTracker.Dialogs;
using FinancialTracker.Views;
using FinancialTracker.Data;
using MaterialDesignThemes.Wpf;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;

namespace FinancialTracker
{
    /// <summary>
    /// منطق التفاعل للنافذة الرئيسية
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly IDataService _dataService;
        private readonly IFileService _fileService;
        private List<Project> _allProjects;
        private DispatcherTimer _clockTimer;

        /// <summary>
        /// منشئ النافذة الرئيسية
        /// </summary>
        public MainWindow(IDataService dataService, IFileService fileService)
        {
            InitializeComponent();

            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            _fileService = fileService ?? throw new ArgumentNullException(nameof(fileService));
            _allProjects = new List<Project>();

            InitializeAsync();
            SetupClock();
        }

        /// <summary>
        /// تهيئة النافذة وتحميل البيانات
        /// </summary>
        private async void InitializeAsync()
        {
            try
            {
                UpdateStatusMessage("جاري تحميل البيانات...");

                // التأكد من وجود قاعدة البيانات
                await _dataService.EnsureDatabaseCreatedAsync();

                // إضافة البيانات الأولية إذا لم تكن موجودة
                var context = App.GetService<FinancialContext>();
                await SampleDataSeeder.SeedSampleDataAsync(context);

                // تحميل البيانات
                await LoadDataAsync();

                UpdateStatusMessage("تم تحميل البيانات بنجاح");
            }
            catch (Exception ex)
            {
                UpdateStatusMessage($"خطأ في تحميل البيانات: {ex.Message}");
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات:\n{ex.Message}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إعداد ساعة الوقت الحقيقي
        /// </summary>
        private void SetupClock()
        {
            _clockTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _clockTimer.Tick += (s, e) => TxtDateTime.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
            _clockTimer.Start();
        }

        /// <summary>
        /// تحميل جميع البيانات
        /// </summary>
        private async Task LoadDataAsync()
        {
            // تحميل المشاريع
            await LoadProjectsAsync();

            // تحديث الإحصائيات
            await UpdateStatisticsAsync();
        }

        /// <summary>
        /// تحميل المشاريع
        /// </summary>
        private async Task LoadProjectsAsync()
        {
            try
            {
                _allProjects = await _dataService.GetProjectsAsync();
                ProjectsDataGrid.ItemsSource = _allProjects;
            }
            catch (Exception ex)
            {
                UpdateStatusMessage($"خطأ في تحميل المشاريع: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث الإحصائيات
        /// </summary>
        private async Task UpdateStatisticsAsync()
        {
            try
            {
                var totalProjects = await _dataService.GetTotalProjectsCountAsync();
                var totalInvoiceAmount = await _dataService.GetTotalInvoiceAmountAsync();
                var totalPaidAmount = await _dataService.GetTotalPaidAmountAsync();
                var totalOutstandingAmount = await _dataService.GetTotalOutstandingAmountAsync();

                TxtTotalProjects.Text = totalProjects.ToString();
                TxtTotalInvoiceAmount.Text = $"{totalInvoiceAmount:N2} ج.م";
                TxtTotalPaidAmount.Text = $"{totalPaidAmount:N2} ج.م";
                TxtTotalOutstandingAmount.Text = $"{totalOutstandingAmount:N2} ج.م";
            }
            catch (Exception ex)
            {
                UpdateStatusMessage($"خطأ في تحديث الإحصائيات: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث رسالة الحالة
        /// </summary>
        private void UpdateStatusMessage(string message)
        {
            TxtStatusMessage.Text = message;
        }

        #region أحداث الأزرار

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private async void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }

        /// <summary>
        /// الإعدادات
        /// </summary>
        private void BtnSettings_Click(object sender, RoutedEventArgs e)
        {
            // TODO: فتح نافذة الإعدادات
            MessageBox.Show("نافذة الإعدادات قيد التطوير", "معلومات",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// حول البرنامج
        /// </summary>
        private void BtnAbout_Click(object sender, RoutedEventArgs e)
        {
            var aboutMessage = "نظام إدارة المشاريع المالية\n" +
                             "الإصدار 1.0\n" +
                             "تم التطوير باستخدام .NET 6 و WPF\n" +
                             "مع Material Design";

            MessageBox.Show(aboutMessage, "حول البرنامج",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// إضافة مشروع جديد
        /// </summary>
        private async void BtnAddProject_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = ProjectDialog.ShowAddDialog(this, _dataService);
                if (result != null)
                {
                    await LoadDataAsync();
                    UpdateStatusMessage($"تم إضافة المشروع '{result.Name}' بنجاح");
                }
            }
            catch (Exception ex)
            {
                UpdateStatusMessage($"خطأ في إضافة المشروع: {ex.Message}");
                MessageBox.Show($"حدث خطأ أثناء إضافة المشروع:\n{ex.Message}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إضافة فاتورة جديدة
        /// </summary>
        private async void BtnAddInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = InvoiceDialog.ShowAddDialog(this, _dataService, _fileService);
                if (result != null)
                {
                    await LoadDataAsync();
                    UpdateStatusMessage($"تم إضافة الفاتورة '{result.InvoiceNumber}' بنجاح");
                }
            }
            catch (Exception ex)
            {
                UpdateStatusMessage($"خطأ في إضافة الفاتورة: {ex.Message}");
                MessageBox.Show($"حدث خطأ أثناء إضافة الفاتورة:\n{ex.Message}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إضافة ارتباط جديد
        /// </summary>
        private async void BtnAddCommitment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = CommitmentDialog.ShowAddDialog(this, _dataService, _fileService);
                if (result != null)
                {
                    await LoadDataAsync();
                    UpdateStatusMessage($"تم إضافة الارتباط '{result.CommitmentNumber}' بنجاح");
                }
            }
            catch (Exception ex)
            {
                UpdateStatusMessage($"خطأ في إضافة الارتباط: {ex.Message}");
                MessageBox.Show($"حدث خطأ أثناء إضافة الارتباط:\n{ex.Message}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// فتح لوحة المعلومات التفصيلية
        /// </summary>
        private void BtnDashboard_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DashboardWindow.ShowDashboard(this, _dataService);
                UpdateStatusMessage("تم فتح لوحة المعلومات التفصيلية");
            }
            catch (Exception ex)
            {
                UpdateStatusMessage($"خطأ في فتح لوحة المعلومات: {ex.Message}");
                MessageBox.Show($"حدث خطأ أثناء فتح لوحة المعلومات:\n{ex.Message}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region أحداث البحث

        /// <summary>
        /// تغيير نص البحث
        /// </summary>
        private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterProjects();
        }

        /// <summary>
        /// البحث
        /// </summary>
        private void BtnSearch_Click(object sender, RoutedEventArgs e)
        {
            FilterProjects();
        }

        /// <summary>
        /// مسح البحث
        /// </summary>
        private void BtnClearSearch_Click(object sender, RoutedEventArgs e)
        {
            TxtSearch.Text = string.Empty;
            FilterProjects();
        }

        /// <summary>
        /// فلترة المشاريع حسب نص البحث
        /// </summary>
        private void FilterProjects()
        {
            var searchTerm = TxtSearch.Text?.Trim().ToLower();

            if (string.IsNullOrEmpty(searchTerm))
            {
                ProjectsDataGrid.ItemsSource = _allProjects;
            }
            else
            {
                var filteredProjects = _allProjects.Where(p =>
                    p.Name.ToLower().Contains(searchTerm) ||
                    (p.Description?.ToLower().Contains(searchTerm) ?? false)
                ).ToList();

                ProjectsDataGrid.ItemsSource = filteredProjects;
            }
        }

        #endregion

        #region أحداث الجدول

        /// <summary>
        /// النقر المزدوج على مشروع
        /// </summary>
        private void ProjectsDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (ProjectsDataGrid.SelectedItem is Project project)
            {
                ViewProjectDetails(project.Id);
            }
        }

        /// <summary>
        /// تغيير التحديد في الجدول
        /// </summary>
        private void ProjectsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ProjectsDataGrid.SelectedItem is Project project)
            {
                UpdateStatusMessage($"تم تحديد المشروع: {project.Name}");
            }
        }

        /// <summary>
        /// عرض تفاصيل المشروع
        /// </summary>
        private void BtnViewProject_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int projectId)
            {
                ViewProjectDetails(projectId);
            }
        }

        /// <summary>
        /// تعديل المشروع
        /// </summary>
        private void BtnEditProject_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int projectId)
            {
                EditProject(projectId);
            }
        }

        /// <summary>
        /// حذف المشروع
        /// </summary>
        private async void BtnDeleteProject_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int projectId)
            {
                await DeleteProject(projectId);
            }
        }

        #endregion

        #region طرق مساعدة

        /// <summary>
        /// عرض تفاصيل المشروع
        /// </summary>
        private void ViewProjectDetails(int projectId)
        {
            // TODO: فتح نافذة تفاصيل المشروع
            MessageBox.Show($"عرض تفاصيل المشروع رقم: {projectId}\n(نافذة التفاصيل قيد التطوير)", "معلومات",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// تعديل المشروع
        /// </summary>
        private async void EditProject(int projectId)
        {
            try
            {
                var project = await _dataService.GetProjectByIdAsync(projectId);
                if (project != null)
                {
                    var result = ProjectDialog.ShowEditDialog(this, _dataService, project);
                    if (result != null)
                    {
                        await LoadDataAsync();
                        UpdateStatusMessage($"تم تحديث المشروع '{result.Name}' بنجاح");
                    }
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على المشروع", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                UpdateStatusMessage($"خطأ في تعديل المشروع: {ex.Message}");
                MessageBox.Show($"حدث خطأ أثناء تعديل المشروع:\n{ex.Message}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حذف المشروع
        /// </summary>
        private async Task DeleteProject(int projectId)
        {
            var project = _allProjects.FirstOrDefault(p => p.Id == projectId);
            if (project == null) return;

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف المشروع '{project.Name}'؟\n" +
                "سيتم حذف جميع البيانات المرتبطة بهذا المشروع.",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    UpdateStatusMessage("جاري حذف المشروع...");

                    var success = await _dataService.DeleteProjectAsync(projectId);
                    if (success)
                    {
                        await LoadDataAsync();
                        UpdateStatusMessage("تم حذف المشروع بنجاح");
                    }
                    else
                    {
                        UpdateStatusMessage("فشل في حذف المشروع");
                        MessageBox.Show("فشل في حذف المشروع", "خطأ",
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    UpdateStatusMessage($"خطأ في حذف المشروع: {ex.Message}");
                    MessageBox.Show($"حدث خطأ أثناء حذف المشروع:\n{ex.Message}",
                                  "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        #endregion

        /// <summary>
        /// تنظيف الموارد عند إغلاق النافذة
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            _clockTimer?.Stop();
            base.OnClosed(e);
        }
    }
}
