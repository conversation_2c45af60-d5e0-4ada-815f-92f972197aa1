using FinancialTracker.Data;
using FinancialTracker.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FinancialTracker.Services
{
    /// <summary>
    /// تطبيق خدمة البيانات - ينفذ العمليات على قاعدة البيانات
    /// </summary>
    public class DataService : IDataService
    {
        private readonly FinancialContext _context;

        /// <summary>
        /// منشئ خدمة البيانات
        /// </summary>
        /// <param name="context">سياق قاعدة البيانات</param>
        public DataService(FinancialContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        #region إدارة المشاريع

        public async Task<List<Project>> GetProjectsAsync()
        {
            return await _context.Projects
                .Include(p => p.Invoices)
                .Include(p => p.Commitments)
                .OrderByDescending(p => p.CreatedDate)
                .ToListAsync();
        }

        public async Task<Project?> GetProjectByIdAsync(int id)
        {
            return await _context.Projects
                .Include(p => p.Invoices)
                    .ThenInclude(i => i.Replies)
                .Include(p => p.Commitments)
                    .ThenInclude(c => c.Replies)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Project> AddProjectAsync(Project project)
        {
            if (project == null)
                throw new ArgumentNullException(nameof(project));

            project.CreatedDate = DateTime.Now;
            _context.Projects.Add(project);
            await _context.SaveChangesAsync();
            return project;
        }

        public async Task<Project> UpdateProjectAsync(Project project)
        {
            if (project == null)
                throw new ArgumentNullException(nameof(project));

            project.LastModifiedDate = DateTime.Now;
            _context.Projects.Update(project);
            await _context.SaveChangesAsync();
            return project;
        }

        public async Task<bool> DeleteProjectAsync(int id)
        {
            var project = await _context.Projects.FindAsync(id);
            if (project == null)
                return false;

            // التحقق من وجود فواتير أو ارتباطات مرتبطة
            var hasInvoices = await _context.Invoices.AnyAsync(i => i.ProjectId == id);
            var hasCommitments = await _context.Commitments.AnyAsync(c => c.ProjectId == id);

            if (hasInvoices || hasCommitments)
            {
                throw new InvalidOperationException("لا يمكن حذف المشروع لوجود فواتير أو ارتباطات مرتبطة به");
            }

            _context.Projects.Remove(project);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<List<Project>> SearchProjectsAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetProjectsAsync();

            return await _context.Projects
                .Include(p => p.Invoices)
                .Include(p => p.Commitments)
                .Where(p => p.Name.Contains(searchTerm) || 
                           (p.Description != null && p.Description.Contains(searchTerm)))
                .OrderByDescending(p => p.CreatedDate)
                .ToListAsync();
        }

        public async Task<List<ProjectSummary>> GetProjectSummariesAsync()
        {
            return await _context.Projects
                .Select(p => new ProjectSummary
                {
                    ProjectId = p.Id,
                    ProjectName = p.Name,
                    Status = p.Status,
                    CreatedDate = p.CreatedDate,
                    InvoiceCount = p.Invoices.Count,
                    TotalInvoiceAmount = p.Invoices.Sum(i => i.Amount),
                    TotalPaidAmount = p.Invoices.Sum(i => i.PaidAmount),
                    CommitmentCount = p.Commitments.Count,
                    TotalCommitmentAmount = p.Commitments.Sum(c => c.Amount),
                    ReplyCount = p.Invoices.SelectMany(i => i.Replies).Count() + 
                                p.Commitments.SelectMany(c => c.Replies).Count(),
                    CompletionPercentage = p.CompletionPercentage,
                    OverdueInvoiceCount = p.Invoices.Count(i => i.DueDate.HasValue && 
                                                               i.DueDate.Value < DateTime.Now && 
                                                               i.PaidAmount < i.Amount),
                    OverdueAmount = p.Invoices.Where(i => i.DueDate.HasValue && 
                                                         i.DueDate.Value < DateTime.Now && 
                                                         i.PaidAmount < i.Amount)
                                              .Sum(i => i.Amount - i.PaidAmount),
                    LastActivityDate = p.LastModifiedDate ?? p.CreatedDate,
                    PlannedBudget = p.PlannedBudget
                })
                .OrderByDescending(p => p.CreatedDate)
                .ToListAsync();
        }

        #endregion

        #region إدارة الفواتير

        public async Task<List<Invoice>> GetInvoicesAsync()
        {
            return await _context.Invoices
                .Include(i => i.Project)
                .Include(i => i.Commitment)
                .Include(i => i.Replies)
                .OrderByDescending(i => i.CreatedDate)
                .ToListAsync();
        }

        public async Task<Invoice?> GetInvoiceByIdAsync(int id)
        {
            return await _context.Invoices
                .Include(i => i.Project)
                .Include(i => i.Commitment)
                .Include(i => i.Replies)
                .FirstOrDefaultAsync(i => i.Id == id);
        }

        public async Task<List<Invoice>> GetInvoicesByProjectIdAsync(int projectId)
        {
            return await _context.Invoices
                .Include(i => i.Commitment)
                .Include(i => i.Replies)
                .Where(i => i.ProjectId == projectId)
                .OrderByDescending(i => i.CreatedDate)
                .ToListAsync();
        }

        public async Task<List<Invoice>> GetInvoicesByCommitmentIdAsync(int commitmentId)
        {
            return await _context.Invoices
                .Include(i => i.Project)
                .Include(i => i.Replies)
                .Where(i => i.CommitmentId == commitmentId)
                .OrderByDescending(i => i.CreatedDate)
                .ToListAsync();
        }

        public async Task<Invoice> AddInvoiceAsync(Invoice invoice)
        {
            if (invoice == null)
                throw new ArgumentNullException(nameof(invoice));

            invoice.CreatedDate = DateTime.Now;
            _context.Invoices.Add(invoice);
            await _context.SaveChangesAsync();
            return invoice;
        }

        public async Task<Invoice> UpdateInvoiceAsync(Invoice invoice)
        {
            if (invoice == null)
                throw new ArgumentNullException(nameof(invoice));

            invoice.LastModifiedDate = DateTime.Now;
            _context.Invoices.Update(invoice);
            await _context.SaveChangesAsync();
            return invoice;
        }

        public async Task<bool> DeleteInvoiceAsync(int id)
        {
            var invoice = await _context.Invoices.FindAsync(id);
            if (invoice == null)
                return false;

            _context.Invoices.Remove(invoice);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<List<Invoice>> SearchInvoicesAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetInvoicesAsync();

            return await _context.Invoices
                .Include(i => i.Project)
                .Include(i => i.Commitment)
                .Include(i => i.Replies)
                .Where(i => i.InvoiceNumber.Contains(searchTerm) || 
                           (i.Description != null && i.Description.Contains(searchTerm)) ||
                           (i.Project != null && i.Project.Name.Contains(searchTerm)))
                .OrderByDescending(i => i.CreatedDate)
                .ToListAsync();
        }

        public async Task<List<Invoice>> GetOverdueInvoicesAsync()
        {
            return await _context.Invoices
                .Include(i => i.Project)
                .Include(i => i.Commitment)
                .Where(i => i.DueDate.HasValue && 
                           i.DueDate.Value < DateTime.Now && 
                           i.PaidAmount < i.Amount)
                .OrderBy(i => i.DueDate)
                .ToListAsync();
        }

        #endregion

        #region إدارة الارتباطات

        public async Task<List<Commitment>> GetCommitmentsAsync()
        {
            return await _context.Commitments
                .Include(c => c.Project)
                .Include(c => c.Invoices)
                .Include(c => c.Replies)
                .OrderByDescending(c => c.CreatedDate)
                .ToListAsync();
        }

        public async Task<Commitment?> GetCommitmentByIdAsync(int id)
        {
            return await _context.Commitments
                .Include(c => c.Project)
                .Include(c => c.Invoices)
                .Include(c => c.Replies)
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        public async Task<List<Commitment>> GetCommitmentsByProjectIdAsync(int projectId)
        {
            return await _context.Commitments
                .Include(c => c.Invoices)
                .Include(c => c.Replies)
                .Where(c => c.ProjectId == projectId)
                .OrderByDescending(c => c.CreatedDate)
                .ToListAsync();
        }

        public async Task<Commitment> AddCommitmentAsync(Commitment commitment)
        {
            if (commitment == null)
                throw new ArgumentNullException(nameof(commitment));

            commitment.CreatedDate = DateTime.Now;
            _context.Commitments.Add(commitment);
            await _context.SaveChangesAsync();
            return commitment;
        }

        public async Task<Commitment> UpdateCommitmentAsync(Commitment commitment)
        {
            if (commitment == null)
                throw new ArgumentNullException(nameof(commitment));

            commitment.LastModifiedDate = DateTime.Now;
            _context.Commitments.Update(commitment);
            await _context.SaveChangesAsync();
            return commitment;
        }

        public async Task<bool> DeleteCommitmentAsync(int id)
        {
            var commitment = await _context.Commitments.FindAsync(id);
            if (commitment == null)
                return false;

            // التحقق من وجود فواتير مرتبطة
            var hasInvoices = await _context.Invoices.AnyAsync(i => i.CommitmentId == id);
            if (hasInvoices)
            {
                throw new InvalidOperationException("لا يمكن حذف الارتباط لوجود فواتير مرتبطة به");
            }

            _context.Commitments.Remove(commitment);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<List<Commitment>> SearchCommitmentsAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetCommitmentsAsync();

            return await _context.Commitments
                .Include(c => c.Project)
                .Include(c => c.Invoices)
                .Include(c => c.Replies)
                .Where(c => c.CommitmentNumber.Contains(searchTerm) || 
                           (c.Description != null && c.Description.Contains(searchTerm)) ||
                           (c.Project != null && c.Project.Name.Contains(searchTerm)))
                .OrderByDescending(c => c.CreatedDate)
                .ToListAsync();
        }

        #endregion

        #region إدارة الردود

        public async Task<List<Reply>> GetRepliesAsync()
        {
            return await _context.Replies
                .Include(r => r.Invoice)
                .Include(r => r.Commitment)
                .OrderByDescending(r => r.CreatedDate)
                .ToListAsync();
        }

        public async Task<Reply?> GetReplyByIdAsync(int id)
        {
            return await _context.Replies
                .Include(r => r.Invoice)
                .Include(r => r.Commitment)
                .FirstOrDefaultAsync(r => r.Id == id);
        }

        public async Task<List<Reply>> GetRepliesByInvoiceIdAsync(int invoiceId)
        {
            return await _context.Replies
                .Include(r => r.Commitment)
                .Where(r => r.InvoiceId == invoiceId)
                .OrderByDescending(r => r.CreatedDate)
                .ToListAsync();
        }

        public async Task<List<Reply>> GetRepliesByCommitmentIdAsync(int commitmentId)
        {
            return await _context.Replies
                .Include(r => r.Invoice)
                .Where(r => r.CommitmentId == commitmentId)
                .OrderByDescending(r => r.CreatedDate)
                .ToListAsync();
        }

        public async Task<Reply> AddReplyAsync(Reply reply)
        {
            if (reply == null)
                throw new ArgumentNullException(nameof(reply));

            reply.CreatedDate = DateTime.Now;
            _context.Replies.Add(reply);
            await _context.SaveChangesAsync();
            return reply;
        }

        public async Task<Reply> UpdateReplyAsync(Reply reply)
        {
            if (reply == null)
                throw new ArgumentNullException(nameof(reply));

            reply.LastModifiedDate = DateTime.Now;
            _context.Replies.Update(reply);
            await _context.SaveChangesAsync();
            return reply;
        }

        public async Task<bool> DeleteReplyAsync(int id)
        {
            var reply = await _context.Replies.FindAsync(id);
            if (reply == null)
                return false;

            _context.Replies.Remove(reply);
            await _context.SaveChangesAsync();
            return true;
        }

        #endregion

        #region الإحصائيات والتقارير

        public async Task<int> GetTotalProjectsCountAsync()
        {
            return await _context.Projects.CountAsync();
        }

        public async Task<decimal> GetTotalInvoiceAmountAsync()
        {
            return await _context.Invoices.SumAsync(i => i.Amount);
        }

        public async Task<decimal> GetTotalPaidAmountAsync()
        {
            return await _context.Invoices.SumAsync(i => i.PaidAmount);
        }

        public async Task<decimal> GetTotalOutstandingAmountAsync()
        {
            var totalAmount = await GetTotalInvoiceAmountAsync();
            var totalPaid = await GetTotalPaidAmountAsync();
            return totalAmount - totalPaid;
        }

        public async Task<Dictionary<string, int>> GetCommitmentStatsByTypeAsync()
        {
            return await _context.Commitments
                .GroupBy(c => c.Type)
                .Select(g => new { Type = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Type, x => x.Count);
        }

        public async Task<Dictionary<string, int>> GetInvoiceStatsByStatusAsync()
        {
            return await _context.Invoices
                .GroupBy(i => i.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Status, x => x.Count);
        }

        #endregion

        #region عمليات عامة

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public async Task EnsureDatabaseCreatedAsync()
        {
            await _context.Database.EnsureCreatedAsync();
        }

        #endregion
    }
}
