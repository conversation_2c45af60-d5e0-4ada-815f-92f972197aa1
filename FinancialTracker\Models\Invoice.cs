using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FinancialTracker.Models
{
    /// <summary>
    /// نموذج الفاتورة - يمثل فاتورة مالية في النظام
    /// </summary>
    public class Invoice
    {
        /// <summary>
        /// معرف الفاتورة الفريد
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// رقم الفاتورة
        /// </summary>
        [Required]
        [StringLength(100)]
        public string InvoiceNumber { get; set; } = string.Empty;

        /// <summary>
        /// مبلغ الفاتورة
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        /// <summary>
        /// المبلغ المدفوع
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; } = 0;

        /// <summary>
        /// تاريخ إصدار الفاتورة
        /// </summary>
        public DateTime IssueDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ استحقاق الفاتورة
        /// </summary>
        public DateTime? DueDate { get; set; }

        /// <summary>
        /// نوع الفاتورة (Task/Service/Other)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Type { get; set; } = "Task";

        /// <summary>
        /// وصف الفاتورة
        /// </summary>
        [StringLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// حالة الفاتورة (Pending/Paid/Overdue/Cancelled)
        /// </summary>
        [StringLength(50)]
        public string Status { get; set; } = "Pending";

        /// <summary>
        /// مسار ملف الفاتورة
        /// </summary>
        [StringLength(500)]
        public string? FilePath { get; set; }

        /// <summary>
        /// تاريخ إنشاء السجل
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime? LastModifiedDate { get; set; }

        /// <summary>
        /// معرف المشروع المرتبط
        /// </summary>
        public int? ProjectId { get; set; }

        /// <summary>
        /// المشروع المرتبط
        /// </summary>
        public virtual Project? Project { get; set; }

        /// <summary>
        /// معرف الارتباط المرتبط
        /// </summary>
        public int? CommitmentId { get; set; }

        /// <summary>
        /// الارتباط المرتبط
        /// </summary>
        public virtual Commitment? Commitment { get; set; }

        /// <summary>
        /// مجموعة الردود المرتبطة بهذه الفاتورة
        /// </summary>
        public virtual ICollection<Reply> Replies { get; set; } = new List<Reply>();

        /// <summary>
        /// المبلغ المستحق (غير المدفوع)
        /// </summary>
        public decimal OutstandingAmount => Amount - PaidAmount;

        /// <summary>
        /// نسبة المبلغ المدفوع
        /// </summary>
        public double PaymentPercentage => Amount > 0 ? (double)(PaidAmount / Amount) * 100 : 0;

        /// <summary>
        /// هل الفاتورة مدفوعة بالكامل؟
        /// </summary>
        public bool IsFullyPaid => PaidAmount >= Amount;

        /// <summary>
        /// هل الفاتورة متأخرة؟
        /// </summary>
        public bool IsOverdue => DueDate.HasValue && DueDate.Value < DateTime.Now && !IsFullyPaid;

        /// <summary>
        /// عدد الأيام المتبقية للاستحقاق
        /// </summary>
        public int? DaysUntilDue => DueDate?.Subtract(DateTime.Now).Days;
    }
}
