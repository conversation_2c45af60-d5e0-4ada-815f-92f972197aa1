using System;

namespace FinancialTracker.Models
{
    /// <summary>
    /// نموذج ملخص المشروع - يحتوي على إحصائيات مجمعة للمشروع
    /// </summary>
    public class ProjectSummary
    {
        /// <summary>
        /// معرف المشروع
        /// </summary>
        public int ProjectId { get; set; }

        /// <summary>
        /// اسم المشروع
        /// </summary>
        public string ProjectName { get; set; } = string.Empty;

        /// <summary>
        /// حالة المشروع
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ إنشاء المشروع
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// عدد الفواتير
        /// </summary>
        public int InvoiceCount { get; set; }

        /// <summary>
        /// إجمالي مبلغ الفواتير
        /// </summary>
        public decimal TotalInvoiceAmount { get; set; }

        /// <summary>
        /// إجمالي المبلغ المدفوع
        /// </summary>
        public decimal TotalPaidAmount { get; set; }

        /// <summary>
        /// المبلغ المستحق
        /// </summary>
        public decimal OutstandingAmount => TotalInvoiceAmount - TotalPaidAmount;

        /// <summary>
        /// عدد الارتباطات
        /// </summary>
        public int CommitmentCount { get; set; }

        /// <summary>
        /// إجمالي مبلغ الارتباطات
        /// </summary>
        public decimal TotalCommitmentAmount { get; set; }

        /// <summary>
        /// عدد الردود
        /// </summary>
        public int ReplyCount { get; set; }

        /// <summary>
        /// نسبة الإنجاز المئوية
        /// </summary>
        public double CompletionPercentage { get; set; }

        /// <summary>
        /// نسبة الدفع المئوية
        /// </summary>
        public double PaymentPercentage => TotalInvoiceAmount > 0 ? (double)(TotalPaidAmount / TotalInvoiceAmount) * 100 : 0;

        /// <summary>
        /// عدد الفواتير المتأخرة
        /// </summary>
        public int OverdueInvoiceCount { get; set; }

        /// <summary>
        /// مبلغ الفواتير المتأخرة
        /// </summary>
        public decimal OverdueAmount { get; set; }

        /// <summary>
        /// آخر تاريخ تحديث
        /// </summary>
        public DateTime? LastActivityDate { get; set; }

        /// <summary>
        /// الميزانية المخططة
        /// </summary>
        public decimal? PlannedBudget { get; set; }

        /// <summary>
        /// الميزانية المستخدمة
        /// </summary>
        public decimal UsedBudget => TotalCommitmentAmount;

        /// <summary>
        /// الميزانية المتبقية
        /// </summary>
        public decimal? RemainingBudget => PlannedBudget - UsedBudget;

        /// <summary>
        /// نسبة استخدام الميزانية
        /// </summary>
        public double? BudgetUtilizationPercentage => PlannedBudget > 0 ? (double)(UsedBudget / PlannedBudget.Value) * 100 : null;
    }
}
