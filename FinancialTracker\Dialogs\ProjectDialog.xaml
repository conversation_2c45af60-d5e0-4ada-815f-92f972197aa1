<Window x:Class="FinancialTracker.Dialogs.ProjectDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="إدارة المشروع" 
        Height="600" Width="500"
        MinHeight="500" MinWidth="450"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="{StaticResource ArabicFont}"
        Background="{StaticResource BackgroundBrush}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        ResizeMode="CanResize">

    <materialDesign:DialogHost Identifier="ProjectDialog">
        <Grid Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- العنوان -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
                <materialDesign:PackIcon Kind="FolderEdit" 
                                       Width="32" Height="32"
                                       VerticalAlignment="Center"
                                       Foreground="{StaticResource PrimaryBrush}"/>
                <TextBlock x:Name="TxtDialogTitle"
                         Text="إضافة مشروع جديد" 
                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                         VerticalAlignment="Center"
                         Margin="12,0,0,0"/>
            </StackPanel>

            <!-- محتوى النموذج -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- اسم المشروع -->
                    <TextBox x:Name="TxtProjectName"
                           Style="{StaticResource ArabicTextBoxStyle}"
                           materialDesign:HintAssist.Hint="اسم المشروع *"
                           materialDesign:HintAssist.IsFloating="True"
                           MaxLength="200"
                           Margin="0,0,0,16"/>

                    <!-- وصف المشروع -->
                    <TextBox x:Name="TxtProjectDescription"
                           Style="{StaticResource ArabicTextBoxStyle}"
                           materialDesign:HintAssist.Hint="وصف المشروع"
                           materialDesign:HintAssist.IsFloating="True"
                           AcceptsReturn="True"
                           TextWrapping="Wrap"
                           MaxLength="1000"
                           Height="100"
                           VerticalScrollBarVisibility="Auto"
                           Margin="0,0,0,16"/>

                    <!-- حالة المشروع -->
                    <ComboBox x:Name="CmbProjectStatus"
                            Style="{StaticResource MaterialDesignComboBox}"
                            materialDesign:HintAssist.Hint="حالة المشروع *"
                            materialDesign:HintAssist.IsFloating="True"
                            FlowDirection="RightToLeft"
                            FontFamily="{StaticResource ArabicFont}"
                            Margin="0,0,0,16">
                        <ComboBoxItem Content="نشط" Tag="Active"/>
                        <ComboBoxItem Content="مكتمل" Tag="Completed"/>
                        <ComboBoxItem Content="ملغي" Tag="Cancelled"/>
                    </ComboBox>

                    <!-- الميزانية المخططة -->
                    <TextBox x:Name="TxtPlannedBudget"
                           Style="{StaticResource ArabicTextBoxStyle}"
                           materialDesign:HintAssist.Hint="الميزانية المخططة (ج.م)"
                           materialDesign:HintAssist.IsFloating="True"
                           Margin="0,0,0,16"/>

                    <!-- التواريخ -->
                    <Grid Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="16"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- تاريخ البداية -->
                        <DatePicker x:Name="DpStartDate"
                                  Grid.Column="0"
                                  Style="{StaticResource MaterialDesignDatePicker}"
                                  materialDesign:HintAssist.Hint="تاريخ البداية"
                                  materialDesign:HintAssist.IsFloating="True"
                                  FlowDirection="RightToLeft"/>

                        <!-- تاريخ النهاية -->
                        <DatePicker x:Name="DpEndDate"
                                  Grid.Column="2"
                                  Style="{StaticResource MaterialDesignDatePicker}"
                                  materialDesign:HintAssist.Hint="تاريخ النهاية المتوقع"
                                  materialDesign:HintAssist.IsFloating="True"
                                  FlowDirection="RightToLeft"/>
                    </Grid>

                    <!-- نسبة الإنجاز -->
                    <StackPanel Margin="0,0,0,16">
                        <TextBlock Text="نسبة الإنجاز (%)" 
                                 Style="{StaticResource MaterialDesignBody2TextBlock}"
                                 Margin="0,0,0,8"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <Slider x:Name="SliderCompletion"
                                  Grid.Column="0"
                                  Style="{StaticResource MaterialDesignSlider}"
                                  Minimum="0"
                                  Maximum="100"
                                  Value="0"
                                  TickFrequency="10"
                                  IsSnapToTickEnabled="True"
                                  ValueChanged="SliderCompletion_ValueChanged"/>
                            
                            <TextBlock x:Name="TxtCompletionValue"
                                     Grid.Column="1"
                                     Text="0%"
                                     Style="{StaticResource MaterialDesignBody1TextBlock}"
                                     VerticalAlignment="Center"
                                     Margin="8,0,0,0"
                                     MinWidth="40"/>
                        </Grid>
                    </StackPanel>

                    <!-- معلومات إضافية (في حالة التعديل) -->
                    <Expander x:Name="ExpanderAdditionalInfo"
                            Header="معلومات إضافية"
                            Style="{StaticResource MaterialDesignExpander}"
                            Visibility="Collapsed"
                            Margin="0,0,0,16">
                        <StackPanel Margin="0,8,0,0">
                            <!-- تاريخ الإنشاء -->
                            <TextBox x:Name="TxtCreatedDate"
                                   Style="{StaticResource ArabicTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="تاريخ الإنشاء"
                                   materialDesign:HintAssist.IsFloating="True"
                                   IsReadOnly="True"
                                   Margin="0,0,0,16"/>

                            <!-- آخر تحديث -->
                            <TextBox x:Name="TxtLastModified"
                                   Style="{StaticResource ArabicTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="آخر تحديث"
                                   materialDesign:HintAssist.IsFloating="True"
                                   IsReadOnly="True"
                                   Margin="0,0,0,16"/>

                            <!-- الإحصائيات -->
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBox x:Name="TxtTotalInvoices"
                                       Grid.Row="0" Grid.Column="0"
                                       Style="{StaticResource ArabicTextBoxStyle}"
                                       materialDesign:HintAssist.Hint="عدد الفواتير"
                                       materialDesign:HintAssist.IsFloating="True"
                                       IsReadOnly="True"
                                       Margin="0,0,8,16"/>

                                <TextBox x:Name="TxtTotalCommitments"
                                       Grid.Row="0" Grid.Column="1"
                                       Style="{StaticResource ArabicTextBoxStyle}"
                                       materialDesign:HintAssist.Hint="عدد الارتباطات"
                                       materialDesign:HintAssist.IsFloating="True"
                                       IsReadOnly="True"
                                       Margin="8,0,0,16"/>

                                <TextBox x:Name="TxtTotalAmount"
                                       Grid.Row="1" Grid.Column="0"
                                       Style="{StaticResource ArabicTextBoxStyle}"
                                       materialDesign:HintAssist.Hint="إجمالي المبلغ"
                                       materialDesign:HintAssist.IsFloating="True"
                                       IsReadOnly="True"
                                       Margin="0,0,8,0"/>

                                <TextBox x:Name="TxtOutstandingAmount"
                                       Grid.Row="1" Grid.Column="1"
                                       Style="{StaticResource ArabicTextBoxStyle}"
                                       materialDesign:HintAssist.Hint="المبلغ المستحق"
                                       materialDesign:HintAssist.IsFloating="True"
                                       IsReadOnly="True"
                                       Margin="8,0,0,0"/>
                            </Grid>
                        </StackPanel>
                    </Expander>
                </StackPanel>
            </ScrollViewer>

            <!-- أزرار الإجراءات -->
            <StackPanel Grid.Row="2" 
                      Orientation="Horizontal" 
                      HorizontalAlignment="Left" 
                      Margin="0,16,0,0">
                
                <Button x:Name="BtnSave"
                      Content="حفظ"
                      Style="{StaticResource ArabicButtonStyle}"
                      Background="{StaticResource PrimaryBrush}"
                      Click="BtnSave_Click"
                      Margin="0,0,8,0"
                      MinWidth="100">
                    <Button.ContentTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ContentSave" 
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="{Binding}" 
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </DataTemplate>
                    </Button.ContentTemplate>
                </Button>

                <Button x:Name="BtnCancel"
                      Content="إلغاء"
                      Style="{StaticResource MaterialDesignOutlinedButton}"
                      Click="BtnCancel_Click"
                      Margin="8,0,0,0"
                      MinWidth="100">
                    <Button.ContentTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Cancel" 
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="{Binding}" 
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </DataTemplate>
                    </Button.ContentTemplate>
                </Button>
            </StackPanel>
        </Grid>
    </materialDesign:DialogHost>
</Window>
