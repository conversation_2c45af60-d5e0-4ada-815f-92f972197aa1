# إنشاء ملف EXE - Financial Tracker

## طرق إنشاء ملف EXE

### الطريقة الأولى: استخدام ملف Batch (الأسهل)

1. **افتح Command Prompt في مجلد المشروع**
2. **شغل الأمر التالي:**
   ```cmd
   publish.bat
   ```
3. **انتظر حتى ينتهي البناء**
4. **ستجد ملف `FinancialTracker.exe` في مجلد `publish`**

### الطريقة الثانية: استخدام PowerShell

1. **افتح PowerShell في مجلد المشروع**
2. **شغل الأمر التالي:**
   ```powershell
   .\publish.ps1
   ```
3. **انتظر حتى ينتهي البناء**

### الطريقة الثالثة: الأوامر المباشرة

#### أ. ملف EXE واحد (مع .NET مدمج)
```cmd
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true -o "./publish"
```

#### ب. ملف EXE صغير (يتطلب .NET 6 مثبت)
```cmd
dotnet publish -c Release -r win-x64 --self-contained false -p:PublishSingleFile=true -o "./publish-small"
```

#### ج. مجلد كامل مع جميع الملفات
```cmd
dotnet publish -c Release -r win-x64 --self-contained true -o "./publish-full"
```

## مواصفات ملف EXE

### النوع الأول: Self-Contained (مستقل)
- **الحجم**: ~150-200 MB
- **المميزات**: 
  - لا يحتاج .NET مثبت على الجهاز
  - يعمل على أي جهاز Windows 10+
  - ملف واحد فقط
- **العيوب**: حجم كبير

### النوع الثاني: Framework-Dependent (يعتمد على .NET)
- **الحجم**: ~10-20 MB
- **المميزات**: حجم صغير
- **العيوب**: يحتاج .NET 6 مثبت على الجهاز

## متطلبات البناء

### على جهازك (للبناء)
- **.NET 6 SDK** مثبت
- **Windows 10** أو أحدث
- **مساحة فارغة**: 2 GB على الأقل

### على الجهاز المستهدف (للتشغيل)
#### للنوع المستقل:
- **Windows 10** (1809) أو أحدث
- **مساحة فارغة**: 500 MB

#### للنوع المعتمد على Framework:
- **Windows 10** (1809) أو أحدث
- **.NET 6 Runtime** مثبت
- **مساحة فارغة**: 100 MB

## خطوات التوزيع

### 1. بناء ملف EXE
```cmd
# شغل أحد الأوامر أعلاه
publish.bat
```

### 2. اختبار الملف
```cmd
# شغل الملف للتأكد من عمله
./publish/FinancialTracker.exe
```

### 3. إنشاء حزمة التوزيع
```cmd
# انسخ الملفات المطلوبة
mkdir "FinancialTracker-v1.0"
copy "./publish/FinancialTracker.exe" "FinancialTracker-v1.0/"
copy "README.md" "FinancialTracker-v1.0/"
copy "LICENSE" "FinancialTracker-v1.0/"
copy "INSTALLATION.md" "FinancialTracker-v1.0/"
```

### 4. ضغط الحزمة
- استخدم WinRAR أو 7-Zip
- اضغط مجلد `FinancialTracker-v1.0`
- اسم الملف: `FinancialTracker-v1.0-Windows.zip`

## استكشاف مشاكل البناء

### مشكلة: "dotnet command not found"
**الحل**: ثبت .NET 6 SDK من الموقع الرسمي

### مشكلة: "Build failed"
**الحل**: 
1. شغل `dotnet clean`
2. شغل `dotnet restore`
3. أعد المحاولة

### مشكلة: "Out of disk space"
**الحل**: 
1. احذف مجلد `bin` و `obj`
2. تأكد من وجود مساحة كافية (2 GB+)

### مشكلة: ملف EXE كبير جداً
**الحل**: استخدم النوع المعتمد على Framework:
```cmd
dotnet publish -c Release -r win-x64 --self-contained false -p:PublishSingleFile=true
```

## تحسين حجم ملف EXE

### 1. إزالة الرموز التصحيحية
```cmd
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:DebugType=None -p:DebugSymbols=false
```

### 2. تفعيل التحسين
```cmd
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=true
```

### 3. ضغط إضافي
```cmd
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:EnableCompressionInSingleFile=true
```

## ملفات الإخراج

بعد البناء الناجح ستجد:

```
publish/
├── FinancialTracker.exe    # الملف التنفيذي الرئيسي
└── (ملفات أخرى حسب نوع البناء)
```

### عند التشغيل الأول سيتم إنشاء:
```
FinancialTracker.exe        # الملف التنفيذي
FinancialTracker.db         # قاعدة البيانات (تُنشأ تلقائياً)
Documents/                  # مجلد الوثائق (يُنشأ تلقائياً)
├── Invoices/              # فواتير
├── Commitments/           # ارتباطات
├── Replies/               # ردود
├── Projects/              # مشاريع
└── Backup/                # نسخ احتياطية
```

## نصائح مهمة

### للمطور
- اختبر ملف EXE على جهاز آخر قبل التوزيع
- احتفظ بنسخة من الكود المصدري
- وثق إصدار .NET المستخدم

### للمستخدم النهائي
- ضع ملف EXE في مجلد منفصل
- لا تحذف ملف قاعدة البيانات
- اعمل نسخة احتياطية من مجلد Documents

### للأمان
- فحص ملف EXE بمكافح الفيروسات قبل التوزيع
- توقيع رقمي للملف (للإصدارات الرسمية)
- تشفير البيانات الحساسة

---

**ملاحظة**: هذا الدليل محدث لـ .NET 6 وقد يحتاج تعديل للإصدارات الأحدث.
