using FinancialTracker.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FinancialTracker.Data
{
    /// <summary>
    /// فئة لإضافة البيانات الأولية التجريبية
    /// </summary>
    public static class SampleDataSeeder
    {
        /// <summary>
        /// إضافة البيانات الأولية إلى قاعدة البيانات
        /// </summary>
        public static async Task SeedSampleDataAsync(FinancialContext context)
        {
            // التحقق من وجود بيانات مسبقاً
            if (context.Projects.Any())
            {
                return; // البيانات موجودة بالفعل
            }

            // إنشاء المشاريع
            var projects = CreateSampleProjects();
            context.Projects.AddRange(projects);
            await context.SaveChangesAsync();

            // إنشاء الارتباطات
            var commitments = CreateSampleCommitments(projects);
            context.Commitments.AddRange(commitments);
            await context.SaveChangesAsync();

            // إنشاء الفواتير
            var invoices = CreateSampleInvoices(projects, commitments);
            context.Invoices.AddRange(invoices);
            await context.SaveChangesAsync();

            // إنشاء الردود
            var replies = CreateSampleReplies(invoices, commitments);
            context.Replies.AddRange(replies);
            await context.SaveChangesAsync();
        }

        /// <summary>
        /// إنشاء مشاريع تجريبية
        /// </summary>
        private static List<Project> CreateSampleProjects()
        {
            return new List<Project>
            {
                new Project
                {
                    Name = "نظام إدارة الجامعة - UMS",
                    Description = "نظام شامل لإدارة شؤون الطلاب والأكاديميين في الجامعة",
                    Status = "Active",
                    CreatedDate = DateTime.Now.AddDays(-45),
                    PlannedBudget = 500000,
                    StartDate = DateTime.Now.AddDays(-40),
                    EndDate = DateTime.Now.AddDays(120),
                    CompletionPercentage = 35
                },
                new Project
                {
                    Name = "نظام البنوك والتمويل - BNG",
                    Description = "منصة متكاملة للخدمات المصرفية والمالية الرقمية",
                    Status = "Active",
                    CreatedDate = DateTime.Now.AddDays(-30),
                    PlannedBudget = 750000,
                    StartDate = DateTime.Now.AddDays(-25),
                    EndDate = DateTime.Now.AddDays(180),
                    CompletionPercentage = 20
                },
                new Project
                {
                    Name = "نظام المصادقة والتفويض - AAA",
                    Description = "نظام أمان متقدم للمصادقة والتحكم في الوصول",
                    Status = "Completed",
                    CreatedDate = DateTime.Now.AddDays(-90),
                    PlannedBudget = 200000,
                    ActualBudget = 185000,
                    StartDate = DateTime.Now.AddDays(-85),
                    EndDate = DateTime.Now.AddDays(-10),
                    CompletionPercentage = 100
                },
                new Project
                {
                    Name = "بروتوكول الوقت الشبكي - NTP",
                    Description = "تطوير خادم وقت دقيق للشبكات المحلية",
                    Status = "Active",
                    CreatedDate = DateTime.Now.AddDays(-20),
                    PlannedBudget = 150000,
                    StartDate = DateTime.Now.AddDays(-15),
                    EndDate = DateTime.Now.AddDays(90),
                    CompletionPercentage = 45
                },
                new Project
                {
                    Name = "منصة التبادل التجاري - HPBX",
                    Description = "منصة عالية الأداء للتجارة الإلكترونية B2B",
                    Status = "Active",
                    CreatedDate = DateTime.Now.AddDays(-10),
                    PlannedBudget = 1000000,
                    StartDate = DateTime.Now.AddDays(-5),
                    EndDate = DateTime.Now.AddDays(240),
                    CompletionPercentage = 10
                }
            };
        }

        /// <summary>
        /// إنشاء ارتباطات تجريبية
        /// </summary>
        private static List<Commitment> CreateSampleCommitments(List<Project> projects)
        {
            var commitments = new List<Commitment>();
            var random = new Random();

            foreach (var project in projects.Take(4)) // أول 4 مشاريع
            {
                for (int i = 1; i <= 3; i++)
                {
                    commitments.Add(new Commitment
                    {
                        CommitmentNumber = $"COM-{project.Name.Split(' ')[0]}-{i:D3}",
                        Amount = random.Next(50000, 200000),
                        IssueDate = DateTime.Now.AddDays(-random.Next(1, 30)),
                        Type = i % 3 == 0 ? "Service" : "Task",
                        Status = "Active",
                        Description = $"ارتباط رقم {i} للمشروع {project.Name}",
                        ProjectId = project.Id,
                        CompletionPercentage = random.Next(0, 80)
                    });
                }
            }

            return commitments;
        }

        /// <summary>
        /// إنشاء فواتير تجريبية
        /// </summary>
        private static List<Invoice> CreateSampleInvoices(List<Project> projects, List<Commitment> commitments)
        {
            var invoices = new List<Invoice>();
            var random = new Random();

            // فواتير مرتبطة بالمشاريع
            foreach (var project in projects)
            {
                for (int i = 1; i <= random.Next(2, 5); i++)
                {
                    var amount = random.Next(25000, 150000);
                    var paidAmount = random.NextDouble() > 0.3 ? random.Next(0, amount) : 0;
                    var issueDate = DateTime.Now.AddDays(-random.Next(1, 60));
                    
                    invoices.Add(new Invoice
                    {
                        InvoiceNumber = $"INV-{project.Name.Split(' ')[0]}-{i:D3}",
                        Amount = amount,
                        PaidAmount = (decimal)paidAmount,
                        IssueDate = issueDate,
                        DueDate = issueDate.AddDays(30),
                        Type = "Task",
                        Status = paidAmount >= amount ? "Paid" : 
                                issueDate.AddDays(30) < DateTime.Now ? "Overdue" : "Pending",
                        Description = $"فاتورة رقم {i} للمشروع {project.Name}",
                        ProjectId = project.Id
                    });
                }
            }

            // فواتير مرتبطة بالارتباطات
            foreach (var commitment in commitments.Take(8))
            {
                var amount = commitment.Amount * 0.6m; // 60% من قيمة الارتباط
                var paidAmount = random.NextDouble() > 0.4 ? amount * (decimal)random.NextDouble() : 0;
                var issueDate = commitment.IssueDate.AddDays(random.Next(5, 15));

                invoices.Add(new Invoice
                {
                    InvoiceNumber = $"INV-{commitment.CommitmentNumber}",
                    Amount = amount,
                    PaidAmount = paidAmount,
                    IssueDate = issueDate,
                    DueDate = issueDate.AddDays(45),
                    Type = commitment.Type,
                    Status = paidAmount >= amount ? "Paid" : 
                            issueDate.AddDays(45) < DateTime.Now ? "Overdue" : "Pending",
                    Description = $"فاتورة للارتباط {commitment.CommitmentNumber}",
                    ProjectId = commitment.ProjectId,
                    CommitmentId = commitment.Id
                });
            }

            return invoices;
        }

        /// <summary>
        /// إنشاء ردود تجريبية
        /// </summary>
        private static List<Reply> CreateSampleReplies(List<Invoice> invoices, List<Commitment> commitments)
        {
            var replies = new List<Reply>();
            var random = new Random();

            // ردود على الفواتير
            foreach (var invoice in invoices.Take(10))
            {
                replies.Add(new Reply
                {
                    ReplyNumber = $"REP-INV-{invoice.InvoiceNumber}",
                    IssueDate = invoice.IssueDate.AddDays(random.Next(1, 10)),
                    Type = "Information",
                    Status = "Processed",
                    Description = $"رد على الفاتورة {invoice.InvoiceNumber}",
                    Priority = "Medium",
                    InvoiceId = invoice.Id,
                    SenderName = "إدارة المالية",
                    ReceiverName = "إدارة المشاريع",
                    IsResponded = true,
                    ResponseDate = DateTime.Now.AddDays(-random.Next(1, 5))
                });
            }

            // ردود على الارتباطات
            foreach (var commitment in commitments.Take(6))
            {
                replies.Add(new Reply
                {
                    ReplyNumber = $"REP-COM-{commitment.CommitmentNumber}",
                    IssueDate = commitment.IssueDate.AddDays(random.Next(2, 15)),
                    Type = "Approval",
                    Status = "Processed",
                    Description = $"موافقة على الارتباط {commitment.CommitmentNumber}",
                    Priority = "High",
                    CommitmentId = commitment.Id,
                    SenderName = "إدارة العقود",
                    ReceiverName = "إدارة المشاريع",
                    IsResponded = true,
                    ResponseDate = DateTime.Now.AddDays(-random.Next(1, 3))
                });
            }

            return replies;
        }
    }
}
