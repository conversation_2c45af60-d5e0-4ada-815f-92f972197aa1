{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\mostafa\\second project\\FinancialTracker\\FinancialTracker.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\mostafa\\second project\\FinancialTracker\\FinancialTracker.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\mostafa\\second project\\FinancialTracker\\FinancialTracker.csproj", "projectName": "FinancialTracker", "projectPath": "C:\\Users\\<USER>\\Desktop\\mostafa\\second project\\FinancialTracker\\FinancialTracker.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\mostafa\\second project\\FinancialTracker\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.25, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[6.0.25, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.25, )"}, "Microsoft.Xaml.Behaviors.Wpf": {"target": "Package", "version": "[1.1.39, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.427\\RuntimeIdentifierGraph.json"}}}}}