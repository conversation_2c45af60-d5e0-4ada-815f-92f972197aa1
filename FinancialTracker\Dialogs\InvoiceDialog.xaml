<Window x:Class="FinancialTracker.Dialogs.InvoiceDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="إدارة الفاتورة" 
        Height="700" Width="550"
        MinHeight="600" MinWidth="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="{StaticResource ArabicFont}"
        Background="{StaticResource BackgroundBrush}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        ResizeMode="CanResize">

    <materialDesign:DialogHost Identifier="InvoiceDialog">
        <Grid Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- العنوان -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
                <materialDesign:PackIcon Kind="FileDocument" 
                                       Width="32" Height="32"
                                       VerticalAlignment="Center"
                                       Foreground="{StaticResource AccentBrush}"/>
                <TextBlock x:Name="TxtDialogTitle"
                         Text="إضافة فاتورة جديدة" 
                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                         VerticalAlignment="Center"
                         Margin="12,0,0,0"/>
            </StackPanel>

            <!-- محتوى النموذج -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- رقم الفاتورة -->
                    <TextBox x:Name="TxtInvoiceNumber"
                           Style="{StaticResource ArabicTextBoxStyle}"
                           materialDesign:HintAssist.Hint="رقم الفاتورة *"
                           materialDesign:HintAssist.IsFloating="True"
                           MaxLength="100"
                           Margin="0,0,0,16"/>

                    <!-- المشروع المرتبط -->
                    <ComboBox x:Name="CmbProject"
                            Style="{StaticResource MaterialDesignComboBox}"
                            materialDesign:HintAssist.Hint="المشروع المرتبط"
                            materialDesign:HintAssist.IsFloating="True"
                            FlowDirection="RightToLeft"
                            FontFamily="{StaticResource ArabicFont}"
                            DisplayMemberPath="Name"
                            SelectedValuePath="Id"
                            Margin="0,0,0,16"/>

                    <!-- الارتباط المرتبط -->
                    <ComboBox x:Name="CmbCommitment"
                            Style="{StaticResource MaterialDesignComboBox}"
                            materialDesign:HintAssist.Hint="الارتباط المرتبط"
                            materialDesign:HintAssist.IsFloating="True"
                            FlowDirection="RightToLeft"
                            FontFamily="{StaticResource ArabicFont}"
                            DisplayMemberPath="CommitmentNumber"
                            SelectedValuePath="Id"
                            Margin="0,0,0,16"/>

                    <!-- المبلغ والمبلغ المدفوع -->
                    <Grid Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="16"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- مبلغ الفاتورة -->
                        <TextBox x:Name="TxtAmount"
                               Grid.Column="0"
                               Style="{StaticResource ArabicTextBoxStyle}"
                               materialDesign:HintAssist.Hint="مبلغ الفاتورة (ج.م) *"
                               materialDesign:HintAssist.IsFloating="True"
                               TextChanged="TxtAmount_TextChanged"/>

                        <!-- المبلغ المدفوع -->
                        <TextBox x:Name="TxtPaidAmount"
                               Grid.Column="2"
                               Style="{StaticResource ArabicTextBoxStyle}"
                               materialDesign:HintAssist.Hint="المبلغ المدفوع (ج.م)"
                               materialDesign:HintAssist.IsFloating="True"
                               TextChanged="TxtPaidAmount_TextChanged"/>
                    </Grid>

                    <!-- المبلغ المستحق -->
                    <TextBox x:Name="TxtOutstandingAmount"
                           Style="{StaticResource ArabicTextBoxStyle}"
                           materialDesign:HintAssist.Hint="المبلغ المستحق (ج.م)"
                           materialDesign:HintAssist.IsFloating="True"
                           IsReadOnly="True"
                           Background="{StaticResource BackgroundBrush}"
                           Margin="0,0,0,16"/>

                    <!-- التواريخ -->
                    <Grid Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="16"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- تاريخ الإصدار -->
                        <DatePicker x:Name="DpIssueDate"
                                  Grid.Column="0"
                                  Style="{StaticResource MaterialDesignDatePicker}"
                                  materialDesign:HintAssist.Hint="تاريخ الإصدار *"
                                  materialDesign:HintAssist.IsFloating="True"
                                  FlowDirection="RightToLeft"/>

                        <!-- تاريخ الاستحقاق -->
                        <DatePicker x:Name="DpDueDate"
                                  Grid.Column="2"
                                  Style="{StaticResource MaterialDesignDatePicker}"
                                  materialDesign:HintAssist.Hint="تاريخ الاستحقاق"
                                  materialDesign:HintAssist.IsFloating="True"
                                  FlowDirection="RightToLeft"/>
                    </Grid>

                    <!-- نوع الفاتورة -->
                    <ComboBox x:Name="CmbInvoiceType"
                            Style="{StaticResource MaterialDesignComboBox}"
                            materialDesign:HintAssist.Hint="نوع الفاتورة *"
                            materialDesign:HintAssist.IsFloating="True"
                            FlowDirection="RightToLeft"
                            FontFamily="{StaticResource ArabicFont}"
                            Margin="0,0,0,16">
                        <ComboBoxItem Content="مهمة" Tag="Task"/>
                        <ComboBoxItem Content="خدمة" Tag="Service"/>
                        <ComboBoxItem Content="أخرى" Tag="Other"/>
                    </ComboBox>

                    <!-- حالة الفاتورة -->
                    <ComboBox x:Name="CmbInvoiceStatus"
                            Style="{StaticResource MaterialDesignComboBox}"
                            materialDesign:HintAssist.Hint="حالة الفاتورة *"
                            materialDesign:HintAssist.IsFloating="True"
                            FlowDirection="RightToLeft"
                            FontFamily="{StaticResource ArabicFont}"
                            Margin="0,0,0,16">
                        <ComboBoxItem Content="في الانتظار" Tag="Pending"/>
                        <ComboBoxItem Content="مدفوعة" Tag="Paid"/>
                        <ComboBoxItem Content="متأخرة" Tag="Overdue"/>
                        <ComboBoxItem Content="ملغية" Tag="Cancelled"/>
                    </ComboBox>

                    <!-- وصف الفاتورة -->
                    <TextBox x:Name="TxtDescription"
                           Style="{StaticResource ArabicTextBoxStyle}"
                           materialDesign:HintAssist.Hint="وصف الفاتورة"
                           materialDesign:HintAssist.IsFloating="True"
                           AcceptsReturn="True"
                           TextWrapping="Wrap"
                           MaxLength="1000"
                           Height="80"
                           VerticalScrollBarVisibility="Auto"
                           Margin="0,0,0,16"/>

                    <!-- ملف الفاتورة -->
                    <Grid Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBox x:Name="TxtFilePath"
                               Grid.Column="0"
                               Style="{StaticResource ArabicTextBoxStyle}"
                               materialDesign:HintAssist.Hint="ملف الفاتورة"
                               materialDesign:HintAssist.IsFloating="True"
                               IsReadOnly="True"/>

                        <Button x:Name="BtnSelectFile"
                              Grid.Column="1"
                              Style="{StaticResource MaterialDesignIconButton}"
                              ToolTip="اختيار ملف"
                              Click="BtnSelectFile_Click"
                              Margin="8,0,0,0">
                            <materialDesign:PackIcon Kind="FileSearch" 
                                                   Width="20" Height="20"/>
                        </Button>

                        <Button x:Name="BtnOpenFile"
                              Grid.Column="2"
                              Style="{StaticResource MaterialDesignIconButton}"
                              ToolTip="فتح الملف"
                              Click="BtnOpenFile_Click"
                              IsEnabled="False"
                              Margin="4,0,0,0">
                            <materialDesign:PackIcon Kind="FileOpen" 
                                                   Width="20" Height="20"/>
                        </Button>
                    </Grid>

                    <!-- معلومات إضافية (في حالة التعديل) -->
                    <Expander x:Name="ExpanderAdditionalInfo"
                            Header="معلومات إضافية"
                            Style="{StaticResource MaterialDesignExpander}"
                            Visibility="Collapsed"
                            Margin="0,0,0,16">
                        <StackPanel Margin="0,8,0,0">
                            <!-- تاريخ الإنشاء -->
                            <TextBox x:Name="TxtCreatedDate"
                                   Style="{StaticResource ArabicTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="تاريخ الإنشاء"
                                   materialDesign:HintAssist.IsFloating="True"
                                   IsReadOnly="True"
                                   Margin="0,0,0,16"/>

                            <!-- آخر تحديث -->
                            <TextBox x:Name="TxtLastModified"
                                   Style="{StaticResource ArabicTextBoxStyle}"
                                   materialDesign:HintAssist.Hint="آخر تحديث"
                                   materialDesign:HintAssist.IsFloating="True"
                                   IsReadOnly="True"
                                   Margin="0,0,0,16"/>

                            <!-- معلومات الدفع -->
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBox x:Name="TxtPaymentPercentage"
                                       Grid.Column="0"
                                       Style="{StaticResource ArabicTextBoxStyle}"
                                       materialDesign:HintAssist.Hint="نسبة الدفع (%)"
                                       materialDesign:HintAssist.IsFloating="True"
                                       IsReadOnly="True"
                                       Margin="0,0,8,0"/>

                                <TextBox x:Name="TxtDaysUntilDue"
                                       Grid.Column="1"
                                       Style="{StaticResource ArabicTextBoxStyle}"
                                       materialDesign:HintAssist.Hint="الأيام حتى الاستحقاق"
                                       materialDesign:HintAssist.IsFloating="True"
                                       IsReadOnly="True"
                                       Margin="8,0,0,0"/>
                            </Grid>
                        </StackPanel>
                    </Expander>
                </StackPanel>
            </ScrollViewer>

            <!-- أزرار الإجراءات -->
            <StackPanel Grid.Row="2" 
                      Orientation="Horizontal" 
                      HorizontalAlignment="Left" 
                      Margin="0,16,0,0">
                
                <Button x:Name="BtnSave"
                      Content="حفظ"
                      Style="{StaticResource ArabicButtonStyle}"
                      Background="{StaticResource AccentBrush}"
                      Click="BtnSave_Click"
                      Margin="0,0,8,0"
                      MinWidth="100">
                    <Button.ContentTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ContentSave" 
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="{Binding}" 
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </DataTemplate>
                    </Button.ContentTemplate>
                </Button>

                <Button x:Name="BtnCancel"
                      Content="إلغاء"
                      Style="{StaticResource MaterialDesignOutlinedButton}"
                      Click="BtnCancel_Click"
                      Margin="8,0,0,0"
                      MinWidth="100">
                    <Button.ContentTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Cancel" 
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="{Binding}" 
                                         VerticalAlignment="Center"/>
                            </StackPanel>
                        </DataTemplate>
                    </Button.ContentTemplate>
                </Button>
            </StackPanel>
        </Grid>
    </materialDesign:DialogHost>
</Window>
