# Financial Tracker - إنشاء ملف EXE
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   إنشاء ملف EXE للتطبيق" -ForegroundColor Yellow
Write-Host "   Financial Tracker - نظام إدارة المشاريع المالية" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

try {
    Write-Host "جاري تنظيف المشروع..." -ForegroundColor Green
    dotnet clean
    
    Write-Host ""
    Write-Host "جاري استعادة الحزم..." -ForegroundColor Green
    dotnet restore
    
    Write-Host ""
    Write-Host "جاري بناء المشروع..." -ForegroundColor Green
    dotnet build -c Release
    
    if ($LASTEXITCODE -ne 0) {
        throw "فشل في بناء المشروع"
    }
    
    Write-Host ""
    Write-Host "جاري إنشاء ملف EXE..." -ForegroundColor Green
    dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true -o "./publish"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "تم إنشاء ملف EXE بنجاح!" -ForegroundColor Green
        Write-Host "المسار: ./publish/FinancialTracker.exe" -ForegroundColor Yellow
        Write-Host "========================================" -ForegroundColor Green
        Write-Host ""
        
        # عرض معلومات الملف
        $exeFile = "./publish/FinancialTracker.exe"
        if (Test-Path $exeFile) {
            $fileInfo = Get-Item $exeFile
            Write-Host "حجم الملف: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -ForegroundColor Cyan
            Write-Host "تاريخ الإنشاء: $($fileInfo.CreationTime)" -ForegroundColor Cyan
        }
        
        Write-Host ""
        $choice = Read-Host "هل تريد تشغيل التطبيق الآن؟ (Y/N)"
        if ($choice -eq "Y" -or $choice -eq "y") {
            Write-Host ""
            Write-Host "جاري تشغيل التطبيق..." -ForegroundColor Green
            Start-Process -FilePath "./publish/FinancialTracker.exe"
        }
    } else {
        throw "فشل في إنشاء ملف EXE"
    }
}
catch {
    Write-Host ""
    Write-Host "خطأ: $_" -ForegroundColor Red
    Write-Host "يرجى التحقق من الأخطاء أعلاه." -ForegroundColor Red
}

Write-Host ""
Read-Host "اضغط Enter للخروج"
