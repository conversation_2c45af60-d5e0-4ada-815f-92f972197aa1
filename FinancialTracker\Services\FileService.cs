using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace FinancialTracker.Services
{
    /// <summary>
    /// تطبيق خدمة الملفات - ينفذ العمليات على الملفات والمجلدات
    /// </summary>
    public class FileService : IFileService
    {
        private readonly string _documentsPath;
        private readonly Dictionary<string, string> _categoryFolders;

        /// <summary>
        /// منشئ خدمة الملفات
        /// </summary>
        public FileService()
        {
            _documentsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Documents");
            _categoryFolders = new Dictionary<string, string>
            {
                { "invoices", "Invoices" },
                { "commitments", "Commitments" },
                { "replies", "Replies" },
                { "projects", "Projects" },
                { "backup", "Backup" }
            };

            EnsureDirectoriesExist();
        }

        public async Task<string> SaveFileAsync(string sourceFilePath, string category, string fileName)
        {
            if (string.IsNullOrWhiteSpace(sourceFilePath))
                throw new ArgumentException("مسار الملف المصدر مطلوب", nameof(sourceFilePath));

            if (string.IsNullOrWhiteSpace(category))
                throw new ArgumentException("فئة الملف مطلوبة", nameof(category));

            if (string.IsNullOrWhiteSpace(fileName))
                throw new ArgumentException("اسم الملف مطلوب", nameof(fileName));

            if (!File.Exists(sourceFilePath))
                throw new FileNotFoundException("الملف المصدر غير موجود", sourceFilePath);

            if (!_categoryFolders.ContainsKey(category.ToLower()))
                throw new ArgumentException($"فئة الملف غير مدعومة: {category}", nameof(category));

            var categoryFolder = _categoryFolders[category.ToLower()];
            var destinationFolder = Path.Combine(_documentsPath, categoryFolder);
            
            // إنشاء اسم ملف فريد إذا كان الملف موجود
            var uniqueFileName = GenerateUniqueFileName(fileName, category);
            var destinationPath = Path.Combine(destinationFolder, uniqueFileName);

            // نسخ الملف
            await Task.Run(() => File.Copy(sourceFilePath, destinationPath, true));

            // إرجاع المسار النسبي
            return Path.Combine(categoryFolder, uniqueFileName);
        }

        public async Task<bool> DeleteFileAsync(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return false;

            try
            {
                var fullPath = GetFullPath(filePath);
                if (File.Exists(fullPath))
                {
                    await Task.Run(() => File.Delete(fullPath));
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> OpenFileAsync(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return false;

            try
            {
                var fullPath = GetFullPath(filePath);
                if (File.Exists(fullPath))
                {
                    await Task.Run(() =>
                    {
                        var processStartInfo = new ProcessStartInfo
                        {
                            FileName = fullPath,
                            UseShellExecute = true
                        };
                        Process.Start(processStartInfo);
                    });
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        public string GetFullPath(string relativePath)
        {
            if (string.IsNullOrWhiteSpace(relativePath))
                return string.Empty;

            return Path.Combine(_documentsPath, relativePath);
        }

        public void EnsureDirectoriesExist()
        {
            // إنشاء مجلد الوثائق الرئيسي
            if (!Directory.Exists(_documentsPath))
                Directory.CreateDirectory(_documentsPath);

            // إنشاء مجلدات الفئات
            foreach (var folder in _categoryFolders.Values)
            {
                var folderPath = Path.Combine(_documentsPath, folder);
                if (!Directory.Exists(folderPath))
                    Directory.CreateDirectory(folderPath);
            }
        }

        public string? SelectFile(string filter = "All Files (*.*)|*.*")
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Filter = filter,
                Title = "اختيار ملف"
            };

            return openFileDialog.ShowDialog() == true ? openFileDialog.FileName : null;
        }

        public string? SelectFolder()
        {
            using var folderDialog = new FolderBrowserDialog
            {
                Description = "اختيار مجلد",
                UseDescriptionForTitle = true
            };

            return folderDialog.ShowDialog() == DialogResult.OK ? folderDialog.SelectedPath : null;
        }

        public async Task<string> CopyFileToDocumentsAsync(string sourceFilePath, string category, string? newFileName = null)
        {
            if (string.IsNullOrWhiteSpace(sourceFilePath))
                throw new ArgumentException("مسار الملف المصدر مطلوب", nameof(sourceFilePath));

            var fileName = newFileName ?? Path.GetFileName(sourceFilePath);
            return await SaveFileAsync(sourceFilePath, category, fileName);
        }

        public bool FileExists(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return false;

            var fullPath = GetFullPath(filePath);
            return File.Exists(fullPath);
        }

        public long GetFileSize(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return -1;

            try
            {
                var fullPath = GetFullPath(filePath);
                if (File.Exists(fullPath))
                {
                    var fileInfo = new FileInfo(fullPath);
                    return fileInfo.Length;
                }
                return -1;
            }
            catch
            {
                return -1;
            }
        }

        public string GetFileExtension(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return string.Empty;

            return Path.GetExtension(filePath);
        }

        public string GenerateUniqueFileName(string originalFileName, string category)
        {
            if (string.IsNullOrWhiteSpace(originalFileName))
                throw new ArgumentException("اسم الملف الأصلي مطلوب", nameof(originalFileName));

            if (!_categoryFolders.ContainsKey(category.ToLower()))
                throw new ArgumentException($"فئة الملف غير مدعومة: {category}", nameof(category));

            var categoryFolder = _categoryFolders[category.ToLower()];
            var destinationFolder = Path.Combine(_documentsPath, categoryFolder);

            var fileName = Path.GetFileNameWithoutExtension(originalFileName);
            var extension = Path.GetExtension(originalFileName);
            var counter = 1;
            var uniqueFileName = originalFileName;

            while (File.Exists(Path.Combine(destinationFolder, uniqueFileName)))
            {
                uniqueFileName = $"{fileName}_{counter}{extension}";
                counter++;
            }

            return uniqueFileName;
        }

        public async Task<List<string>> GetFilesByCategoryAsync(string category)
        {
            if (string.IsNullOrWhiteSpace(category))
                return new List<string>();

            if (!_categoryFolders.ContainsKey(category.ToLower()))
                return new List<string>();

            var categoryFolder = _categoryFolders[category.ToLower()];
            var folderPath = Path.Combine(_documentsPath, categoryFolder);

            if (!Directory.Exists(folderPath))
                return new List<string>();

            return await Task.Run(() =>
            {
                var files = Directory.GetFiles(folderPath);
                var relativeFiles = new List<string>();

                foreach (var file in files)
                {
                    var relativePath = Path.Combine(categoryFolder, Path.GetFileName(file));
                    relativeFiles.Add(relativePath);
                }

                return relativeFiles;
            });
        }

        public async Task<bool> CreateBackupAsync(string backupPath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(backupPath))
                    return false;

                await Task.Run(() =>
                {
                    if (Directory.Exists(backupPath))
                        Directory.Delete(backupPath, true);

                    Directory.CreateDirectory(backupPath);

                    // نسخ جميع الملفات
                    CopyDirectory(_documentsPath, backupPath);
                });

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> RestoreFromBackupAsync(string backupPath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(backupPath) || !Directory.Exists(backupPath))
                    return false;

                await Task.Run(() =>
                {
                    if (Directory.Exists(_documentsPath))
                        Directory.Delete(_documentsPath, true);

                    Directory.CreateDirectory(_documentsPath);

                    // استعادة جميع الملفات
                    CopyDirectory(backupPath, _documentsPath);
                });

                return true;
            }
            catch
            {
                return false;
            }
        }

        private void CopyDirectory(string sourceDir, string destDir)
        {
            var dir = new DirectoryInfo(sourceDir);
            if (!dir.Exists)
                return;

            var dirs = dir.GetDirectories();
            Directory.CreateDirectory(destDir);

            foreach (var file in dir.GetFiles())
            {
                var targetFilePath = Path.Combine(destDir, file.Name);
                file.CopyTo(targetFilePath);
            }

            foreach (var subDir in dirs)
            {
                var newDestinationDir = Path.Combine(destDir, subDir.Name);
                CopyDirectory(subDir.FullName, newDestinationDir);
            }
        }
    }
}
