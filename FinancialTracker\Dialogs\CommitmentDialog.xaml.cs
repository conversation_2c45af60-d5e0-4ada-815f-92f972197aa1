using FinancialTracker.Models;
using FinancialTracker.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace FinancialTracker.Dialogs
{
    /// <summary>
    /// منطق التفاعل لنافذة حوار الارتباط
    /// </summary>
    public partial class CommitmentDialog : Window
    {
        private readonly IDataService _dataService;
        private readonly IFileService _fileService;
        private Commitment? _currentCommitment;
        private bool _isEditMode;
        private List<Project> _projects;

        /// <summary>
        /// الارتباط الحالي بعد الحفظ
        /// </summary>
        public Commitment? Result { get; private set; }

        /// <summary>
        /// منشئ نافذة إضافة ارتباط جديد
        /// </summary>
        public CommitmentDialog(IDataService dataService, IFileService fileService)
        {
            InitializeComponent();
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            _fileService = fileService ?? throw new ArgumentNullException(nameof(fileService));
            _isEditMode = false;
            _projects = new List<Project>();
            
            InitializeAsync();
        }

        /// <summary>
        /// منشئ نافذة تعديل ارتباط موجود
        /// </summary>
        public CommitmentDialog(IDataService dataService, IFileService fileService, Commitment commitment)
        {
            InitializeComponent();
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            _fileService = fileService ?? throw new ArgumentNullException(nameof(fileService));
            _currentCommitment = commitment ?? throw new ArgumentNullException(nameof(commitment));
            _isEditMode = true;
            _projects = new List<Project>();
            
            InitializeAsync();
        }

        /// <summary>
        /// تهيئة النافذة وتحميل البيانات
        /// </summary>
        private async void InitializeAsync()
        {
            try
            {
                // تحميل المشاريع
                _projects = await _dataService.GetProjectsAsync();
                CmbProject.ItemsSource = _projects;

                if (_isEditMode)
                {
                    InitializeForEditCommitment();
                }
                else
                {
                    InitializeForNewCommitment();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات:\n{ex.Message}", 
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تهيئة النافذة لإضافة ارتباط جديد
        /// </summary>
        private void InitializeForNewCommitment()
        {
            TxtDialogTitle.Text = "إضافة ارتباط جديد";
            DpIssueDate.SelectedDate = DateTime.Now;
            CmbCommitmentType.SelectedIndex = 0; // Task
            CmbCommitmentStatus.SelectedIndex = 0; // Active
            SliderCompletion.Value = 0;
        }

        /// <summary>
        /// تهيئة النافذة لتعديل ارتباط موجود
        /// </summary>
        private void InitializeForEditCommitment()
        {
            if (_currentCommitment == null) return;

            TxtDialogTitle.Text = "تعديل الارتباط";
            
            // تعبئة البيانات الأساسية
            TxtCommitmentNumber.Text = _currentCommitment.CommitmentNumber;
            TxtAmount.Text = _currentCommitment.Amount.ToString("F2");
            TxtDescription.Text = _currentCommitment.Description;
            TxtFilePath.Text = _currentCommitment.FilePath;
            DpIssueDate.SelectedDate = _currentCommitment.IssueDate;
            SliderCompletion.Value = _currentCommitment.CompletionPercentage;

            // تحديد المشروع
            if (_currentCommitment.ProjectId.HasValue)
            {
                CmbProject.SelectedValue = _currentCommitment.ProjectId.Value;
            }

            // تحديد نوع الارتباط
            foreach (ComboBoxItem item in CmbCommitmentType.Items)
            {
                if (item.Tag?.ToString() == _currentCommitment.Type)
                {
                    CmbCommitmentType.SelectedItem = item;
                    break;
                }
            }

            // تحديد حالة الارتباط
            foreach (ComboBoxItem item in CmbCommitmentStatus.Items)
            {
                if (item.Tag?.ToString() == _currentCommitment.Status)
                {
                    CmbCommitmentStatus.SelectedItem = item;
                    break;
                }
            }

            UpdateFileButtons();
        }

        /// <summary>
        /// تحديث قيمة نسبة الإنجاز
        /// </summary>
        private void SliderCompletion_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (TxtCompletionValue != null)
            {
                TxtCompletionValue.Text = $"{e.NewValue:F0}%";
            }
        }

        /// <summary>
        /// اختيار ملف
        /// </summary>
        private async void BtnSelectFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var filter = "PDF Files (*.pdf)|*.pdf|Word Documents (*.docx;*.doc)|*.docx;*.doc|Excel Files (*.xlsx;*.xls)|*.xlsx;*.xls|All Files (*.*)|*.*";
                var selectedFile = _fileService.SelectFile(filter);
                
                if (!string.IsNullOrEmpty(selectedFile))
                {
                    // نسخ الملف إلى مجلد الارتباطات
                    var fileName = System.IO.Path.GetFileName(selectedFile);
                    var relativePath = await _fileService.SaveFileAsync(selectedFile, "commitments", fileName);
                    
                    TxtFilePath.Text = relativePath;
                    UpdateFileButtons();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء اختيار الملف:\n{ex.Message}", 
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// فتح الملف
        /// </summary>
        private async void BtnOpenFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(TxtFilePath.Text))
                {
                    var success = await _fileService.OpenFileAsync(TxtFilePath.Text);
                    if (!success)
                    {
                        MessageBox.Show("فشل في فتح الملف. تأكد من وجود الملف.", 
                                      "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح الملف:\n{ex.Message}", 
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث حالة أزرار الملف
        /// </summary>
        private void UpdateFileButtons()
        {
            BtnOpenFile.IsEnabled = !string.IsNullOrEmpty(TxtFilePath.Text) && 
                                   _fileService.FileExists(TxtFilePath.Text);
        }

        /// <summary>
        /// حفظ الارتباط
        /// </summary>
        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                // إنشاء أو تحديث الارتباط
                var commitment = _isEditMode ? _currentCommitment! : new Commitment();
                
                // تعبئة البيانات
                commitment.CommitmentNumber = TxtCommitmentNumber.Text.Trim();
                commitment.Amount = decimal.Parse(TxtAmount.Text);
                commitment.Description = string.IsNullOrWhiteSpace(TxtDescription.Text) 
                    ? null : TxtDescription.Text.Trim();
                commitment.FilePath = string.IsNullOrWhiteSpace(TxtFilePath.Text) 
                    ? null : TxtFilePath.Text.Trim();
                commitment.IssueDate = DpIssueDate.SelectedDate ?? DateTime.Now;
                commitment.CompletionPercentage = SliderCompletion.Value;

                if (CmbProject.SelectedValue != null)
                {
                    commitment.ProjectId = (int)CmbProject.SelectedValue;
                }

                if (CmbCommitmentType.SelectedItem is ComboBoxItem typeItem)
                {
                    commitment.Type = typeItem.Tag?.ToString() ?? "Task";
                }

                if (CmbCommitmentStatus.SelectedItem is ComboBoxItem statusItem)
                {
                    commitment.Status = statusItem.Tag?.ToString() ?? "Active";
                }

                // حفظ في قاعدة البيانات
                if (_isEditMode)
                {
                    Result = await _dataService.UpdateCommitmentAsync(commitment);
                }
                else
                {
                    Result = await _dataService.AddCommitmentAsync(commitment);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ الارتباط:\n{ex.Message}", 
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// التحقق من صحة البيانات المدخلة
        /// </summary>
        private bool ValidateInput()
        {
            // التحقق من رقم الارتباط
            if (string.IsNullOrWhiteSpace(TxtCommitmentNumber.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الارتباط", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtCommitmentNumber.Focus();
                return false;
            }

            // التحقق من مبلغ الارتباط
            if (!decimal.TryParse(TxtAmount.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح للارتباط", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtAmount.Focus();
                return false;
            }

            // التحقق من تاريخ الإصدار
            if (!DpIssueDate.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ الإصدار", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                DpIssueDate.Focus();
                return false;
            }

            // التحقق من نوع الارتباط
            if (CmbCommitmentType.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع الارتباط", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbCommitmentType.Focus();
                return false;
            }

            // التحقق من حالة الارتباط
            if (CmbCommitmentStatus.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار حالة الارتباط", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbCommitmentStatus.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// عرض نافذة إضافة ارتباط جديد
        /// </summary>
        public static Commitment? ShowAddDialog(Window owner, IDataService dataService, IFileService fileService)
        {
            var dialog = new CommitmentDialog(dataService, fileService)
            {
                Owner = owner
            };

            return dialog.ShowDialog() == true ? dialog.Result : null;
        }

        /// <summary>
        /// عرض نافذة تعديل ارتباط موجود
        /// </summary>
        public static Commitment? ShowEditDialog(Window owner, IDataService dataService, IFileService fileService, Commitment commitment)
        {
            var dialog = new CommitmentDialog(dataService, fileService, commitment)
            {
                Owner = owner
            };

            return dialog.ShowDialog() == true ? dialog.Result : null;
        }
    }
}
