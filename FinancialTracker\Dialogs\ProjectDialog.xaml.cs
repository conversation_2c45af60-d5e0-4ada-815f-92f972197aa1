using FinancialTracker.Models;
using FinancialTracker.Services;
using System;
using System.Windows;
using System.Windows.Controls;

namespace FinancialTracker.Dialogs
{
    /// <summary>
    /// منطق التفاعل لنافذة حوار المشروع
    /// </summary>
    public partial class ProjectDialog : Window
    {
        private readonly IDataService _dataService;
        private Project? _currentProject;
        private bool _isEditMode;

        /// <summary>
        /// المشروع الحالي بعد الحفظ
        /// </summary>
        public Project? Result { get; private set; }

        /// <summary>
        /// منشئ نافذة إضافة مشروع جديد
        /// </summary>
        public ProjectDialog(IDataService dataService)
        {
            InitializeComponent();
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            _isEditMode = false;
            
            InitializeForNewProject();
        }

        /// <summary>
        /// منشئ نافذة تعديل مشروع موجود
        /// </summary>
        public ProjectDialog(IDataService dataService, Project project)
        {
            InitializeComponent();
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            _currentProject = project ?? throw new ArgumentNullException(nameof(project));
            _isEditMode = true;
            
            InitializeForEditProject();
        }

        /// <summary>
        /// تهيئة النافذة لإضافة مشروع جديد
        /// </summary>
        private void InitializeForNewProject()
        {
            TxtDialogTitle.Text = "إضافة مشروع جديد";
            CmbProjectStatus.SelectedIndex = 0; // Active
            SliderCompletion.Value = 0;
            ExpanderAdditionalInfo.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// تهيئة النافذة لتعديل مشروع موجود
        /// </summary>
        private void InitializeForEditProject()
        {
            if (_currentProject == null) return;

            TxtDialogTitle.Text = "تعديل المشروع";
            
            // تعبئة البيانات الأساسية
            TxtProjectName.Text = _currentProject.Name;
            TxtProjectDescription.Text = _currentProject.Description;
            TxtPlannedBudget.Text = _currentProject.PlannedBudget?.ToString("F2");
            DpStartDate.SelectedDate = _currentProject.StartDate;
            DpEndDate.SelectedDate = _currentProject.EndDate;
            SliderCompletion.Value = _currentProject.CompletionPercentage;

            // تحديد حالة المشروع
            foreach (ComboBoxItem item in CmbProjectStatus.Items)
            {
                if (item.Tag?.ToString() == _currentProject.Status)
                {
                    CmbProjectStatus.SelectedItem = item;
                    break;
                }
            }

            // عرض المعلومات الإضافية
            ExpanderAdditionalInfo.Visibility = Visibility.Visible;
            TxtCreatedDate.Text = _currentProject.CreatedDate.ToString("dd/MM/yyyy HH:mm");
            TxtLastModified.Text = _currentProject.LastModifiedDate?.ToString("dd/MM/yyyy HH:mm") ?? "لم يتم التحديث";
            
            // الإحصائيات
            TxtTotalInvoices.Text = _currentProject.Invoices?.Count.ToString() ?? "0";
            TxtTotalCommitments.Text = _currentProject.Commitments?.Count.ToString() ?? "0";
            TxtTotalAmount.Text = $"{_currentProject.TotalInvoiceAmount:F2} ج.م";
            TxtOutstandingAmount.Text = $"{_currentProject.OutstandingAmount:F2} ج.م";
        }

        /// <summary>
        /// تحديث قيمة نسبة الإنجاز
        /// </summary>
        private void SliderCompletion_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (TxtCompletionValue != null)
            {
                TxtCompletionValue.Text = $"{e.NewValue:F0}%";
            }
        }

        /// <summary>
        /// حفظ المشروع
        /// </summary>
        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                // إنشاء أو تحديث المشروع
                var project = _isEditMode ? _currentProject! : new Project();
                
                // تعبئة البيانات
                project.Name = TxtProjectName.Text.Trim();
                project.Description = string.IsNullOrWhiteSpace(TxtProjectDescription.Text) 
                    ? null : TxtProjectDescription.Text.Trim();
                
                if (CmbProjectStatus.SelectedItem is ComboBoxItem statusItem)
                {
                    project.Status = statusItem.Tag?.ToString() ?? "Active";
                }

                if (decimal.TryParse(TxtPlannedBudget.Text, out decimal plannedBudget))
                {
                    project.PlannedBudget = plannedBudget;
                }

                project.StartDate = DpStartDate.SelectedDate;
                project.EndDate = DpEndDate.SelectedDate;
                project.CompletionPercentage = SliderCompletion.Value;

                // حفظ في قاعدة البيانات
                if (_isEditMode)
                {
                    Result = await _dataService.UpdateProjectAsync(project);
                }
                else
                {
                    Result = await _dataService.AddProjectAsync(project);
                }

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ المشروع:\n{ex.Message}", 
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// التحقق من صحة البيانات المدخلة
        /// </summary>
        private bool ValidateInput()
        {
            // التحقق من اسم المشروع
            if (string.IsNullOrWhiteSpace(TxtProjectName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المشروع", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtProjectName.Focus();
                return false;
            }

            // التحقق من حالة المشروع
            if (CmbProjectStatus.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار حالة المشروع", "خطأ في البيانات", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbProjectStatus.Focus();
                return false;
            }

            // التحقق من الميزانية المخططة
            if (!string.IsNullOrWhiteSpace(TxtPlannedBudget.Text))
            {
                if (!decimal.TryParse(TxtPlannedBudget.Text, out decimal budget) || budget < 0)
                {
                    MessageBox.Show("يرجى إدخال قيمة صحيحة للميزانية المخططة", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtPlannedBudget.Focus();
                    return false;
                }
            }

            // التحقق من التواريخ
            if (DpStartDate.SelectedDate.HasValue && DpEndDate.SelectedDate.HasValue)
            {
                if (DpStartDate.SelectedDate.Value > DpEndDate.SelectedDate.Value)
                {
                    MessageBox.Show("تاريخ البداية يجب أن يكون قبل تاريخ النهاية", "خطأ في البيانات", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    DpStartDate.Focus();
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// عرض نافذة إضافة مشروع جديد
        /// </summary>
        public static Project? ShowAddDialog(Window owner, IDataService dataService)
        {
            var dialog = new ProjectDialog(dataService)
            {
                Owner = owner
            };

            return dialog.ShowDialog() == true ? dialog.Result : null;
        }

        /// <summary>
        /// عرض نافذة تعديل مشروع موجود
        /// </summary>
        public static Project? ShowEditDialog(Window owner, IDataService dataService, Project project)
        {
            var dialog = new ProjectDialog(dataService, project)
            {
                Owner = owner
            };

            return dialog.ShowDialog() == true ? dialog.Result : null;
        }
    }
}
