using FinancialTracker.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FinancialTracker.Services
{
    /// <summary>
    /// واجهة خدمة البيانات - تحدد العمليات المتاحة على البيانات
    /// </summary>
    public interface IDataService
    {
        #region إدارة المشاريع

        /// <summary>
        /// الحصول على جميع المشاريع
        /// </summary>
        Task<List<Project>> GetProjectsAsync();

        /// <summary>
        /// الحصول على مشروع بالمعرف
        /// </summary>
        Task<Project?> GetProjectByIdAsync(int id);

        /// <summary>
        /// إضافة مشروع جديد
        /// </summary>
        Task<Project> AddProjectAsync(Project project);

        /// <summary>
        /// تحديث مشروع موجود
        /// </summary>
        Task<Project> UpdateProjectAsync(Project project);

        /// <summary>
        /// حذف مشروع
        /// </summary>
        Task<bool> DeleteProjectAsync(int id);

        /// <summary>
        /// البحث في المشاريع
        /// </summary>
        Task<List<Project>> SearchProjectsAsync(string searchTerm);

        /// <summary>
        /// الحصول على ملخص المشاريع
        /// </summary>
        Task<List<ProjectSummary>> GetProjectSummariesAsync();

        #endregion

        #region إدارة الفواتير

        /// <summary>
        /// الحصول على جميع الفواتير
        /// </summary>
        Task<List<Invoice>> GetInvoicesAsync();

        /// <summary>
        /// الحصول على فاتورة بالمعرف
        /// </summary>
        Task<Invoice?> GetInvoiceByIdAsync(int id);

        /// <summary>
        /// الحصول على فواتير مشروع محدد
        /// </summary>
        Task<List<Invoice>> GetInvoicesByProjectIdAsync(int projectId);

        /// <summary>
        /// الحصول على فواتير ارتباط محدد
        /// </summary>
        Task<List<Invoice>> GetInvoicesByCommitmentIdAsync(int commitmentId);

        /// <summary>
        /// إضافة فاتورة جديدة
        /// </summary>
        Task<Invoice> AddInvoiceAsync(Invoice invoice);

        /// <summary>
        /// تحديث فاتورة موجودة
        /// </summary>
        Task<Invoice> UpdateInvoiceAsync(Invoice invoice);

        /// <summary>
        /// حذف فاتورة
        /// </summary>
        Task<bool> DeleteInvoiceAsync(int id);

        /// <summary>
        /// البحث في الفواتير
        /// </summary>
        Task<List<Invoice>> SearchInvoicesAsync(string searchTerm);

        /// <summary>
        /// الحصول على الفواتير المتأخرة
        /// </summary>
        Task<List<Invoice>> GetOverdueInvoicesAsync();

        #endregion

        #region إدارة الارتباطات

        /// <summary>
        /// الحصول على جميع الارتباطات
        /// </summary>
        Task<List<Commitment>> GetCommitmentsAsync();

        /// <summary>
        /// الحصول على ارتباط بالمعرف
        /// </summary>
        Task<Commitment?> GetCommitmentByIdAsync(int id);

        /// <summary>
        /// الحصول على ارتباطات مشروع محدد
        /// </summary>
        Task<List<Commitment>> GetCommitmentsByProjectIdAsync(int projectId);

        /// <summary>
        /// إضافة ارتباط جديد
        /// </summary>
        Task<Commitment> AddCommitmentAsync(Commitment commitment);

        /// <summary>
        /// تحديث ارتباط موجود
        /// </summary>
        Task<Commitment> UpdateCommitmentAsync(Commitment commitment);

        /// <summary>
        /// حذف ارتباط
        /// </summary>
        Task<bool> DeleteCommitmentAsync(int id);

        /// <summary>
        /// البحث في الارتباطات
        /// </summary>
        Task<List<Commitment>> SearchCommitmentsAsync(string searchTerm);

        #endregion

        #region إدارة الردود

        /// <summary>
        /// الحصول على جميع الردود
        /// </summary>
        Task<List<Reply>> GetRepliesAsync();

        /// <summary>
        /// الحصول على رد بالمعرف
        /// </summary>
        Task<Reply?> GetReplyByIdAsync(int id);

        /// <summary>
        /// الحصول على ردود فاتورة محددة
        /// </summary>
        Task<List<Reply>> GetRepliesByInvoiceIdAsync(int invoiceId);

        /// <summary>
        /// الحصول على ردود ارتباط محدد
        /// </summary>
        Task<List<Reply>> GetRepliesByCommitmentIdAsync(int commitmentId);

        /// <summary>
        /// إضافة رد جديد
        /// </summary>
        Task<Reply> AddReplyAsync(Reply reply);

        /// <summary>
        /// تحديث رد موجود
        /// </summary>
        Task<Reply> UpdateReplyAsync(Reply reply);

        /// <summary>
        /// حذف رد
        /// </summary>
        Task<bool> DeleteReplyAsync(int id);

        #endregion

        #region الإحصائيات والتقارير

        /// <summary>
        /// الحصول على إجمالي عدد المشاريع
        /// </summary>
        Task<int> GetTotalProjectsCountAsync();

        /// <summary>
        /// الحصول على إجمالي مبلغ الفواتير
        /// </summary>
        Task<decimal> GetTotalInvoiceAmountAsync();

        /// <summary>
        /// الحصول على إجمالي المبلغ المدفوع
        /// </summary>
        Task<decimal> GetTotalPaidAmountAsync();

        /// <summary>
        /// الحصول على إجمالي المبلغ المستحق
        /// </summary>
        Task<decimal> GetTotalOutstandingAmountAsync();

        /// <summary>
        /// الحصول على إحصائيات الارتباطات حسب النوع
        /// </summary>
        Task<Dictionary<string, int>> GetCommitmentStatsByTypeAsync();

        /// <summary>
        /// الحصول على إحصائيات الفواتير حسب الحالة
        /// </summary>
        Task<Dictionary<string, int>> GetInvoiceStatsByStatusAsync();

        #endregion

        #region عمليات عامة

        /// <summary>
        /// حفظ التغييرات في قاعدة البيانات
        /// </summary>
        Task<int> SaveChangesAsync();

        /// <summary>
        /// التحقق من وجود قاعدة البيانات وإنشاؤها إذا لم تكن موجودة
        /// </summary>
        Task EnsureDatabaseCreatedAsync();

        #endregion
    }
}
