using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using System;
using System.IO;

namespace FinancialTracker.Data
{
    /// <summary>
    /// مصنع إنشاء DbContext في وقت التصميم - مطلوب لأدوات Entity Framework
    /// </summary>
    public class DesignTimeDbContextFactory : IDesignTimeDbContextFactory<FinancialContext>
    {
        /// <summary>
        /// إنشاء DbContext في وقت التصميم
        /// </summary>
        /// <param name="args">المعاملات</param>
        /// <returns>سياق قاعدة البيانات</returns>
        public FinancialContext CreateDbContext(string[] args)
        {
            var optionsBuilder = new DbContextOptionsBuilder<FinancialContext>();
            
            // مسار قاعدة البيانات
            var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "FinancialTracker.db");
            optionsBuilder.UseSqlite($"Data Source={dbPath}");

            return new FinancialContext();
        }
    }
}
