========================================
Financial Tracker - Project Management System
Version 1.0
========================================

Welcome to the Financial Tracker Project Management System!

This system helps you with:
✅ Managing financial projects
✅ Tracking invoices and payments
✅ Managing financial commitments
✅ Following up on replies and correspondence
✅ Viewing reports and statistics

========================================
How to Create EXE File (For Developers)
========================================

Easiest Method:
1. Double-click on file: CREATE_EXE.bat
2. Wait for the build to complete
3. You'll find FinancialTracker.exe in the publish folder

Or use the following commands in Command Prompt:
dotnet clean
dotnet restore
dotnet build -c Release
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -o "./publish"

========================================
How to Run the Application
========================================

If you have a ready EXE file:
1. Double-click on FinancialTracker.exe
2. Database will be created automatically
3. Sample data will be added automatically

If you have the source code:
1. Double-click on run.bat
2. Or use the command: dotnet run

========================================
System Requirements
========================================

For standalone file (self-contained):
- Windows 10 or newer
- 500 MB free space
- No additional software needed

For development:
- .NET 6 SDK
- Visual Studio 2022 or VS Code

========================================
الميزات الرئيسية
========================================

🏗️ إدارة المشاريع:
- إضافة وتعديل المشاريع
- تتبع الميزانية والإنجاز
- ربط التواريخ والحالات

📄 إدارة الفواتير:
- إنشاء فواتير مرتبطة بالمشاريع
- تتبع المدفوعات والمستحقات
- رفع ملفات الفواتير

🔗 إدارة الارتباطات:
- ارتباطات مالية للمشاريع
- تتبع نسبة الإنجاز
- إدارة الملفات المرفقة

💬 نظام الردود:
- ردود على الفواتير والارتباطات
- تصنيف الأولوية والحالة
- تتبع المرسل والمستقبل

📊 التقارير والإحصائيات:
- لوحة معلومات تفاعلية
- إحصائيات مالية شاملة
- تقارير حسب الحالة والنوع

========================================
البيانات الأولية
========================================

يحتوي التطبيق على بيانات تجريبية:
- 10 مشاريع (UMS, BNG, AAA, NTP, HPBX, إلخ)
- 12 ارتباط مالي
- 25+ فاتورة بحالات مختلفة
- 16 رد على الفواتير والارتباطات

========================================
الملفات المهمة
========================================

بعد التشغيل ستجد:
📁 FinancialTracker.exe - الملف التنفيذي
📁 FinancialTracker.db - قاعدة البيانات
📁 Documents/ - مجلد الوثائق
   ├── Invoices/ - فواتير
   ├── Commitments/ - ارتباطات
   ├── Replies/ - ردود
   ├── Projects/ - مشاريع
   └── Backup/ - نسخ احتياطية

⚠️ مهم: لا تحذف ملف قاعدة البيانات (.db)

========================================
استكشاف المشاكل
========================================

إذا لم يعمل التطبيق:
1. تأكد من أن Windows محدث
2. شغل الملف كمسؤول (Run as Administrator)
3. تأكد من عدم حجب مكافح الفيروسات
4. تأكد من وجود مساحة كافية (500 MB+)

إذا ظهرت رسالة خطأ .NET:
1. حمل .NET 6 من موقع Microsoft
2. ثبته وأعد تشغيل الجهاز

========================================
الدعم والمساعدة
========================================

للمزيد من المعلومات راجع:
📖 README.md - دليل شامل
📖 DEVELOPER_GUIDE.md - دليل المطور  
📖 INSTALLATION.md - دليل التثبيت
📖 BUILD_EXE.md - دليل إنشاء EXE

========================================
معلومات التطوير
========================================

تم تطوير هذا النظام باستخدام:
- .NET 6 WPF
- SQLite Database
- Entity Framework Core
- Material Design UI
- دعم كامل للغة العربية

الإصدار: 1.0.0
التاريخ: يوليو 2025
الترخيص: MIT License

========================================
شكراً لاستخدام نظام إدارة المشاريع المالية!
========================================
