========================================
نظام إدارة المشاريع المالية
Financial Tracker v1.0
========================================

مرحباً بك في نظام إدارة المشاريع المالية!

هذا النظام يساعدك في:
✅ إدارة المشاريع المالية
✅ تتبع الفواتير والمدفوعات  
✅ إدارة الارتباطات المالية
✅ متابعة الردود والمراسلات
✅ عرض التقارير والإحصائيات

========================================
كيفية إنشاء ملف EXE (للمطورين)
========================================

الطريقة الأسهل:
1. انقر نقرة مزدوجة على ملف: إنشاء_EXE.bat
2. انتظر حتى ينتهي البناء
3. ستجد ملف FinancialTracker.exe في مجلد publish

أو استخدم الأوامر التالية في Command Prompt:
dotnet clean
dotnet restore  
dotnet build -c Release
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -o "./publish"

========================================
كيفية تشغيل التطبيق
========================================

إذا كان لديك ملف EXE جاهز:
1. انقر نقرة مزدوجة على FinancialTracker.exe
2. سيتم إنشاء قاعدة البيانات تلقائياً
3. ستُضاف البيانات الأولية التجريبية

إذا كان لديك الكود المصدري:
1. انقر نقرة مزدوجة على run.bat
2. أو استخدم الأمر: dotnet run

========================================
متطلبات التشغيل
========================================

للملف المستقل (self-contained):
- Windows 10 أو أحدث
- 500 MB مساحة فارغة
- لا يحتاج برامج إضافية

للتطوير:
- .NET 6 SDK
- Visual Studio 2022 أو VS Code

========================================
الميزات الرئيسية
========================================

🏗️ إدارة المشاريع:
- إضافة وتعديل المشاريع
- تتبع الميزانية والإنجاز
- ربط التواريخ والحالات

📄 إدارة الفواتير:
- إنشاء فواتير مرتبطة بالمشاريع
- تتبع المدفوعات والمستحقات
- رفع ملفات الفواتير

🔗 إدارة الارتباطات:
- ارتباطات مالية للمشاريع
- تتبع نسبة الإنجاز
- إدارة الملفات المرفقة

💬 نظام الردود:
- ردود على الفواتير والارتباطات
- تصنيف الأولوية والحالة
- تتبع المرسل والمستقبل

📊 التقارير والإحصائيات:
- لوحة معلومات تفاعلية
- إحصائيات مالية شاملة
- تقارير حسب الحالة والنوع

========================================
البيانات الأولية
========================================

يحتوي التطبيق على بيانات تجريبية:
- 10 مشاريع (UMS, BNG, AAA, NTP, HPBX, إلخ)
- 12 ارتباط مالي
- 25+ فاتورة بحالات مختلفة
- 16 رد على الفواتير والارتباطات

========================================
الملفات المهمة
========================================

بعد التشغيل ستجد:
📁 FinancialTracker.exe - الملف التنفيذي
📁 FinancialTracker.db - قاعدة البيانات
📁 Documents/ - مجلد الوثائق
   ├── Invoices/ - فواتير
   ├── Commitments/ - ارتباطات
   ├── Replies/ - ردود
   ├── Projects/ - مشاريع
   └── Backup/ - نسخ احتياطية

⚠️ مهم: لا تحذف ملف قاعدة البيانات (.db)

========================================
استكشاف المشاكل
========================================

إذا لم يعمل التطبيق:
1. تأكد من أن Windows محدث
2. شغل الملف كمسؤول (Run as Administrator)
3. تأكد من عدم حجب مكافح الفيروسات
4. تأكد من وجود مساحة كافية (500 MB+)

إذا ظهرت رسالة خطأ .NET:
1. حمل .NET 6 من موقع Microsoft
2. ثبته وأعد تشغيل الجهاز

========================================
الدعم والمساعدة
========================================

للمزيد من المعلومات راجع:
📖 README.md - دليل شامل
📖 DEVELOPER_GUIDE.md - دليل المطور  
📖 INSTALLATION.md - دليل التثبيت
📖 BUILD_EXE.md - دليل إنشاء EXE

========================================
معلومات التطوير
========================================

تم تطوير هذا النظام باستخدام:
- .NET 6 WPF
- SQLite Database
- Entity Framework Core
- Material Design UI
- دعم كامل للغة العربية

الإصدار: 1.0.0
التاريخ: يوليو 2025
الترخيص: MIT License

========================================
شكراً لاستخدام نظام إدارة المشاريع المالية!
========================================
