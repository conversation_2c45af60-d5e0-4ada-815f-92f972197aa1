using System;
using System.ComponentModel.DataAnnotations;

namespace FinancialTracker.Models
{
    /// <summary>
    /// نموذج الرد - يمثل رد على فاتورة أو ارتباط في النظام
    /// </summary>
    public class Reply
    {
        /// <summary>
        /// معرف الرد الفريد
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// رقم الرد
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ReplyNumber { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ إصدار الرد
        /// </summary>
        public DateTime IssueDate { get; set; } = DateTime.Now;

        /// <summary>
        /// وصف الرد
        /// </summary>
        [StringLength(1000)]
        public string? Description { get; set; }

        /// <summary>
        /// نوع الرد (Approval/Rejection/Modification/Information)
        /// </summary>
        [StringLength(50)]
        public string Type { get; set; } = "Information";

        /// <summary>
        /// حالة الرد (Pending/Processed/Archived)
        /// </summary>
        [StringLength(50)]
        public string Status { get; set; } = "Pending";

        /// <summary>
        /// مسار ملف الرد
        /// </summary>
        [StringLength(500)]
        public string? FilePath { get; set; }

        /// <summary>
        /// تاريخ إنشاء السجل
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime? LastModifiedDate { get; set; }

        /// <summary>
        /// معرف الفاتورة المرتبطة (اختياري)
        /// </summary>
        public int? InvoiceId { get; set; }

        /// <summary>
        /// الفاتورة المرتبطة
        /// </summary>
        public virtual Invoice? Invoice { get; set; }

        /// <summary>
        /// معرف الارتباط المرتبط (اختياري)
        /// </summary>
        public int? CommitmentId { get; set; }

        /// <summary>
        /// الارتباط المرتبط
        /// </summary>
        public virtual Commitment? Commitment { get; set; }

        /// <summary>
        /// أولوية الرد (Low/Medium/High/Critical)
        /// </summary>
        [StringLength(20)]
        public string Priority { get; set; } = "Medium";

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        [StringLength(2000)]
        public string? Notes { get; set; }

        /// <summary>
        /// اسم المرسل
        /// </summary>
        [StringLength(200)]
        public string? SenderName { get; set; }

        /// <summary>
        /// اسم المستقبل
        /// </summary>
        [StringLength(200)]
        public string? ReceiverName { get; set; }

        /// <summary>
        /// تاريخ الاستجابة المطلوب
        /// </summary>
        public DateTime? ResponseDueDate { get; set; }

        /// <summary>
        /// هل تم الرد عليه؟
        /// </summary>
        public bool IsResponded { get; set; } = false;

        /// <summary>
        /// تاريخ الرد الفعلي
        /// </summary>
        public DateTime? ResponseDate { get; set; }
    }
}
