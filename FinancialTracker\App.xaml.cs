﻿using FinancialTracker.Data;
using FinancialTracker.Services;
using FinancialTracker.Views;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Windows;

namespace FinancialTracker
{
    /// <summary>
    /// منطق التفاعل لـ App.xaml
    /// </summary>
    public partial class App : Application
    {
        private ServiceProvider? _serviceProvider;

        /// <summary>
        /// إعداد الخدمات عند بدء التطبيق
        /// </summary>
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // إعداد حاوي الخدمات
            var services = new ServiceCollection();
            ConfigureServices(services);
            _serviceProvider = services.BuildServiceProvider();

            // إنشاء النافذة الرئيسية
            var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
            mainWindow.Show();
        }

        /// <summary>
        /// تكوين الخدمات
        /// </summary>
        private void ConfigureServices(ServiceCollection services)
        {
            // إضافة DbContext
            services.AddSingleton<FinancialContext>();

            // إضافة الخدمات
            services.AddSingleton<IDataService, DataService>();
            services.AddSingleton<IFileService, FileService>();

            // إضافة النوافذ
            services.AddTransient<MainWindow>();
            services.AddTransient<DashboardWindow>();
        }

        /// <summary>
        /// تنظيف الموارد عند إغلاق التطبيق
        /// </summary>
        protected override void OnExit(ExitEventArgs e)
        {
            _serviceProvider?.Dispose();
            base.OnExit(e);
        }

        /// <summary>
        /// الحصول على خدمة من حاوي الخدمات
        /// </summary>
        public static T GetService<T>() where T : class
        {
            return ((App)Current)._serviceProvider?.GetService<T>()
                ?? throw new InvalidOperationException($"Service {typeof(T).Name} not found");
        }
    }
}
