using FinancialTracker.Models;
using FinancialTracker.Services;
using FinancialTracker.Dialogs;
using FinancialTracker.Views;
using FinancialTracker.Data;
using MaterialDesignThemes.Wpf;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;

namespace FinancialTracker
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly IDataService _dataService;
        private readonly IFileService _fileService;
        private List<Project> _allProjects;
        private DispatcherTimer _clockTimer;

        /// <summary>
        /// Main window constructor
        /// </summary>
        public MainWindow(IDataService dataService, IFileService fileService)
        {
            InitializeComponent();

            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            _fileService = fileService ?? throw new ArgumentNullException(nameof(fileService));
            _allProjects = new List<Project>();

            InitializeAsync();
            SetupClock();
        }

        /// <summary>
        /// Initialize window and load data
        /// </summary>
        private async void InitializeAsync()
        {
            try
            {
                UpdateStatusMessage("Loading data...");

                // Ensure database is created
                await _dataService.EnsureDatabaseCreatedAsync();

                // Add sample data if not exists
                var context = App.GetService<FinancialContext>();
                await SampleDataSeeder.SeedSampleDataAsync(context);

                // Load data
                await LoadDataAsync();

                UpdateStatusMessage("Data loaded successfully");
            }
            catch (Exception ex)
            {
                UpdateStatusMessage($"Error loading data: {ex.Message}");
                MessageBox.Show($"An error occurred while loading data:\n{ex.Message}",
                              "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Setup real-time clock
        /// </summary>
        private void SetupClock()
        {
            _clockTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _clockTimer.Tick += (s, e) => TxtDateTime.Text = DateTime.Now.ToString("MM/dd/yyyy HH:mm:ss");
            _clockTimer.Start();
        }

        /// <summary>
        /// Load all data
        /// </summary>
        private async Task LoadDataAsync()
        {
            // Load projects
            await LoadProjectsAsync();

            // Update statistics
            await UpdateStatisticsAsync();
        }

        /// <summary>
        /// Load projects
        /// </summary>
        private async Task LoadProjectsAsync()
        {
            try
            {
                _allProjects = await _dataService.GetProjectsAsync();
                ProjectsDataGrid.ItemsSource = _allProjects;
            }
            catch (Exception ex)
            {
                UpdateStatusMessage($"Error loading projects: {ex.Message}");
            }
        }

        /// <summary>
        /// Update statistics
        /// </summary>
        private async Task UpdateStatisticsAsync()
        {
            try
            {
                var totalProjects = await _dataService.GetTotalProjectsCountAsync();
                var totalInvoiceAmount = await _dataService.GetTotalInvoiceAmountAsync();
                var totalPaidAmount = await _dataService.GetTotalPaidAmountAsync();
                var totalOutstandingAmount = await _dataService.GetTotalOutstandingAmountAsync();

                TxtTotalProjects.Text = totalProjects.ToString();
                TxtTotalInvoiceAmount.Text = $"${totalInvoiceAmount:N2}";
                TxtTotalPaidAmount.Text = $"${totalPaidAmount:N2}";
                TxtTotalOutstandingAmount.Text = $"${totalOutstandingAmount:N2}";
            }
            catch (Exception ex)
            {
                UpdateStatusMessage($"Error updating statistics: {ex.Message}");
            }
        }

        /// <summary>
        /// Update status message
        /// </summary>
        private void UpdateStatusMessage(string message)
        {
            TxtStatusMessage.Text = message;
        }

        #region Button Events

        /// <summary>
        /// Refresh data
        /// </summary>
        private async void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
            UpdateStatusMessage("Data refreshed successfully");
        }

        /// <summary>
        /// Show settings
        /// </summary>
        private void BtnSettings_Click(object sender, RoutedEventArgs e)
        {
            UpdateStatusMessage("Settings feature coming soon");
        }

        /// <summary>
        /// Show about dialog
        /// </summary>
        private void BtnAbout_Click(object sender, RoutedEventArgs e)
        {
            var aboutMessage = "Financial Tracker - Project Management System\n" +
                             "Version 1.0\n" +
                             "Developed using .NET 6 and WPF\n" +
                             "with Material Design";

            MessageBox.Show(aboutMessage, "About",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// Add new project
        /// </summary>
        private async void BtnAddProject_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = ProjectDialog.ShowAddDialog(this, _dataService);
                if (result != null)
                {
                    await LoadDataAsync();
                    UpdateStatusMessage($"Project '{result.Name}' added successfully");
                }
            }
            catch (Exception ex)
            {
                UpdateStatusMessage($"Error adding project: {ex.Message}");
                MessageBox.Show($"An error occurred while adding project:\n{ex.Message}",
                              "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Add new invoice
        /// </summary>
        private async void BtnAddInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = InvoiceDialog.ShowAddDialog(this, _dataService, _fileService);
                if (result != null)
                {
                    await LoadDataAsync();
                    UpdateStatusMessage($"Invoice '{result.InvoiceNumber}' added successfully");
                }
            }
            catch (Exception ex)
            {
                UpdateStatusMessage($"Error adding invoice: {ex.Message}");
                MessageBox.Show($"An error occurred while adding invoice:\n{ex.Message}",
                              "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Add new commitment
        /// </summary>
        private async void BtnAddCommitment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = CommitmentDialog.ShowAddDialog(this, _dataService, _fileService);
                if (result != null)
                {
                    await LoadDataAsync();
                    UpdateStatusMessage($"Commitment '{result.CommitmentNumber}' added successfully");
                }
            }
            catch (Exception ex)
            {
                UpdateStatusMessage($"Error adding commitment: {ex.Message}");
                MessageBox.Show($"An error occurred while adding commitment:\n{ex.Message}",
                              "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Open detailed dashboard
        /// </summary>
        private void BtnDashboard_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DashboardWindow.ShowDashboard(this, _dataService);
                UpdateStatusMessage("Detailed dashboard opened");
            }
            catch (Exception ex)
            {
                UpdateStatusMessage($"Error opening dashboard: {ex.Message}");
                MessageBox.Show($"An error occurred while opening dashboard:\n{ex.Message}",
                              "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Search and Filter

        /// <summary>
        /// Search text changed
        /// </summary>
        private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterProjects();
        }

        /// <summary>
        /// Search button click
        /// </summary>
        private void BtnSearch_Click(object sender, RoutedEventArgs e)
        {
            FilterProjects();
        }

        /// <summary>
        /// Clear search button click
        /// </summary>
        private void BtnClearSearch_Click(object sender, RoutedEventArgs e)
        {
            TxtSearch.Text = "";
            FilterProjects();
        }

        /// <summary>
        /// Filter projects based on search text
        /// </summary>
        private void FilterProjects()
        {
            if (_allProjects == null) return;

            var searchText = TxtSearch.Text?.ToLower() ?? "";
            
            if (string.IsNullOrWhiteSpace(searchText))
            {
                ProjectsDataGrid.ItemsSource = _allProjects;
            }
            else
            {
                var filteredProjects = _allProjects.Where(p =>
                    p.Name.ToLower().Contains(searchText) ||
                    p.Description.ToLower().Contains(searchText) ||
                    p.Status.ToLower().Contains(searchText)
                ).ToList();
                
                ProjectsDataGrid.ItemsSource = filteredProjects;
            }
        }

        #endregion

        #region DataGrid Events

        /// <summary>
        /// Project selection changed
        /// </summary>
        private void ProjectsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ProjectsDataGrid.SelectedItem is Project project)
            {
                UpdateStatusMessage($"Selected project: {project.Name}");
            }
        }

        /// <summary>
        /// View project details
        /// </summary>
        private void ProjectsDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (ProjectsDataGrid.SelectedItem is Project project)
            {
                BtnViewProject_Click(sender, new RoutedEventArgs());
            }
        }

        #endregion

        #region Project Actions

        /// <summary>
        /// View project details
        /// </summary>
        private void BtnViewProject_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var projectId = GetProjectIdFromButton(sender);
                if (projectId > 0)
                {
                    var project = _allProjects.FirstOrDefault(p => p.Id == projectId);
                    if (project != null)
                    {
                        ProjectDialog.ShowViewDialog(this, project);
                        UpdateStatusMessage($"Viewing project: {project.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                UpdateStatusMessage($"Error viewing project: {ex.Message}");
            }
        }

        /// <summary>
        /// Edit project
        /// </summary>
        private async void BtnEditProject_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var projectId = GetProjectIdFromButton(sender);
                if (projectId > 0)
                {
                    var project = _allProjects.FirstOrDefault(p => p.Id == projectId);
                    if (project != null)
                    {
                        var result = ProjectDialog.ShowEditDialog(this, _dataService, project);
                        if (result != null)
                        {
                            await LoadDataAsync();
                            UpdateStatusMessage($"Project '{result.Name}' updated successfully");
                        }
                    }
                    else
                    {
                        MessageBox.Show("Project not found", "Error",
                                      MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                UpdateStatusMessage($"Error editing project: {ex.Message}");
                MessageBox.Show($"An error occurred while editing project:\n{ex.Message}",
                              "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Delete project
        /// </summary>
        private async void BtnDeleteProject_Click(object sender, RoutedEventArgs e)
        {
            var projectId = GetProjectIdFromButton(sender);
            if (projectId <= 0) return;

            var project = _allProjects.FirstOrDefault(p => p.Id == projectId);
            if (project == null) return;

            var result = MessageBox.Show(
                $"Are you sure you want to delete project '{project.Name}'?\n" +
                "All related data will be deleted.",
                "Confirm Delete",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    UpdateStatusMessage("Deleting project...");

                    var success = await _dataService.DeleteProjectAsync(projectId);
                    if (success)
                    {
                        await LoadDataAsync();
                        UpdateStatusMessage("Project deleted successfully");
                    }
                    else
                    {
                        UpdateStatusMessage("Failed to delete project");
                        MessageBox.Show("Failed to delete project", "Error",
                                      MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    UpdateStatusMessage($"Error deleting project: {ex.Message}");
                    MessageBox.Show($"An error occurred while deleting project:\n{ex.Message}",
                                  "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// Get project ID from button tag
        /// </summary>
        private int GetProjectIdFromButton(object sender)
        {
            if (sender is Button button && button.Tag != null)
            {
                if (int.TryParse(button.Tag.ToString(), out int projectId))
                {
                    return projectId;
                }
            }
            return 0;
        }

        #endregion
    }
}
