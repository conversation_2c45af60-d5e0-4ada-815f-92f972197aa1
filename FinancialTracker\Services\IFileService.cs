using System;
using System.Threading.Tasks;

namespace FinancialTracker.Services
{
    /// <summary>
    /// واجهة خدمة الملفات - تحدد العمليات المتاحة على الملفات
    /// </summary>
    public interface IFileService
    {
        /// <summary>
        /// حفظ ملف في المجلد المناسب
        /// </summary>
        /// <param name="sourceFilePath">مسار الملف المصدر</param>
        /// <param name="category">فئة الملف (invoices/commitments/replies/projects)</param>
        /// <param name="fileName">اسم الملف الجديد</param>
        /// <returns>المسار النسبي للملف المحفوظ</returns>
        Task<string> SaveFileAsync(string sourceFilePath, string category, string fileName);

        /// <summary>
        /// حذف ملف
        /// </summary>
        /// <param name="filePath">المسار النسبي للملف</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        Task<bool> DeleteFileAsync(string filePath);

        /// <summary>
        /// فتح ملف باستخدام التطبيق الافتراضي
        /// </summary>
        /// <param name="filePath">المسار النسبي للملف</param>
        /// <returns>true إذا تم فتح الملف بنجاح</returns>
        Task<bool> OpenFileAsync(string filePath);

        /// <summary>
        /// الحصول على المسار الكامل للملف
        /// </summary>
        /// <param name="relativePath">المسار النسبي</param>
        /// <returns>المسار الكامل</returns>
        string GetFullPath(string relativePath);

        /// <summary>
        /// التأكد من وجود المجلدات المطلوبة
        /// </summary>
        void EnsureDirectoriesExist();

        /// <summary>
        /// اختيار ملف من النظام
        /// </summary>
        /// <param name="filter">فلتر أنواع الملفات</param>
        /// <returns>مسار الملف المختار أو null</returns>
        string? SelectFile(string filter = "All Files (*.*)|*.*");

        /// <summary>
        /// اختيار مجلد من النظام
        /// </summary>
        /// <returns>مسار المجلد المختار أو null</returns>
        string? SelectFolder();

        /// <summary>
        /// نسخ ملف إلى مجلد الوثائق
        /// </summary>
        /// <param name="sourceFilePath">مسار الملف المصدر</param>
        /// <param name="category">فئة الملف</param>
        /// <param name="newFileName">الاسم الجديد للملف (اختياري)</param>
        /// <returns>المسار النسبي للملف المنسوخ</returns>
        Task<string> CopyFileToDocumentsAsync(string sourceFilePath, string category, string? newFileName = null);

        /// <summary>
        /// التحقق من وجود ملف
        /// </summary>
        /// <param name="filePath">المسار النسبي للملف</param>
        /// <returns>true إذا كان الملف موجود</returns>
        bool FileExists(string filePath);

        /// <summary>
        /// الحصول على حجم الملف بالبايت
        /// </summary>
        /// <param name="filePath">المسار النسبي للملف</param>
        /// <returns>حجم الملف أو -1 إذا لم يكن موجود</returns>
        long GetFileSize(string filePath);

        /// <summary>
        /// الحصول على امتداد الملف
        /// </summary>
        /// <param name="filePath">مسار الملف</param>
        /// <returns>امتداد الملف</returns>
        string GetFileExtension(string filePath);

        /// <summary>
        /// إنشاء اسم ملف فريد
        /// </summary>
        /// <param name="originalFileName">الاسم الأصلي للملف</param>
        /// <param name="category">فئة الملف</param>
        /// <returns>اسم ملف فريد</returns>
        string GenerateUniqueFileName(string originalFileName, string category);

        /// <summary>
        /// الحصول على قائمة بجميع الملفات في فئة معينة
        /// </summary>
        /// <param name="category">فئة الملف</param>
        /// <returns>قائمة بمسارات الملفات</returns>
        Task<List<string>> GetFilesByCategoryAsync(string category);

        /// <summary>
        /// إنشاء نسخة احتياطية من الملفات
        /// </summary>
        /// <param name="backupPath">مسار النسخة الاحتياطية</param>
        /// <returns>true إذا تم إنشاء النسخة الاحتياطية بنجاح</returns>
        Task<bool> CreateBackupAsync(string backupPath);

        /// <summary>
        /// استعادة الملفات من نسخة احتياطية
        /// </summary>
        /// <param name="backupPath">مسار النسخة الاحتياطية</param>
        /// <returns>true إذا تم الاستعادة بنجاح</returns>
        Task<bool> RestoreFromBackupAsync(string backupPath);
    }
}
