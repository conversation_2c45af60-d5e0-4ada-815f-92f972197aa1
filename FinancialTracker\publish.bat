@echo off
echo ========================================
echo    إنشاء ملف EXE للتطبيق
echo    Financial Tracker - نظام إدارة المشاريع المالية
echo ========================================
echo.

echo جاري تنظيف المشروع...
dotnet clean

echo.
echo جاري استعادة الحزم...
dotnet restore

echo.
echo جاري بناء المشروع...
dotnet build -c Release

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo فشل في بناء المشروع!
    pause
    exit /b 1
)

echo.
echo جاري إنشاء ملف EXE...
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true -o "./publish"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo تم إنشاء ملف EXE بنجاح!
    echo المسار: ./publish/FinancialTracker.exe
    echo ========================================
    echo.
    echo هل تريد تشغيل التطبيق الآن؟ (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo.
        echo جاري تشغيل التطبيق...
        start "" "./publish/FinancialTracker.exe"
    )
) else (
    echo.
    echo فشل في إنشاء ملف EXE!
    echo يرجى التحقق من الأخطاء أعلاه.
)

echo.
pause
