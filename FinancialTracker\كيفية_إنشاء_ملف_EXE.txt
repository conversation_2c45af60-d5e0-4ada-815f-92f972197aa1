========================================
كيفية إنشاء ملف EXE للتطبيق
Financial Tracker - نظام إدارة المشاريع المالية
========================================

الطريقة الأولى: استخدام ملف Batch (الأسهل)
===========================================

1. افتح Command Prompt (cmd) في مجلد المشروع
2. اكتب الأمر التالي واضغط Enter:
   publish.bat

3. انتظر حتى ينتهي البناء
4. ستجد ملف FinancialTracker.exe في مجلد publish


الطريقة الثانية: الأوامر المباشرة
===============================

1. افتح Command Prompt في مجلد المشروع
2. اكتب الأوامر التالية واحد تلو الآخر:

   dotnet clean
   dotnet restore
   dotnet build -c Release
   dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -o "./publish"

3. ستجد ملف FinancialTracker.exe في مجلد publish


الطريقة الثالثة: ملف صغير الحجم (يحتاج .NET 6)
===========================================

إذا كان .NET 6 مثبت على الجهاز المستهدف:

dotnet publish -c Release -r win-x64 --self-contained false -p:PublishSingleFile=true -o "./publish-small"


معلومات مهمة:
=============

- الطريقة الأولى تنشئ ملف كبير (~150 MB) لكن يعمل على أي جهاز
- الطريقة الثالثة تنشئ ملف صغير (~10 MB) لكن يحتاج .NET 6 مثبت
- قاعدة البيانات تُنشأ تلقائياً عند أول تشغيل
- البيانات الأولية تُضاف تلقائياً


متطلبات التشغيل:
================

للملف الكبير (self-contained):
- Windows 10 أو أحدث
- لا يحتاج أي برامج إضافية

للملف الصغير (framework-dependent):
- Windows 10 أو أحدث  
- .NET 6 Runtime مثبت


استكشاف المشاكل:
================

إذا ظهر خطأ "dotnet command not found":
- ثبت .NET 6 SDK من موقع Microsoft

إذا فشل البناء:
- تأكد من وجود مساحة كافية على القرص (2 GB+)
- أغلق أي برامج مكافحة فيروسات مؤقتاً
- شغل Command Prompt كمسؤول (Run as Administrator)

إذا لم يعمل ملف EXE:
- تأكد من أن Windows محدث
- شغل الملف كمسؤول
- تأكد من عدم حجب مكافح الفيروسات للملف


ملفات التطبيق بعد التشغيل:
=========================

FinancialTracker.exe     - الملف التنفيذي
FinancialTracker.db      - قاعدة البيانات (تُنشأ تلقائياً)
Documents/               - مجلد الوثائق (يُنشأ تلقائياً)
  ├── Invoices/         - فواتير
  ├── Commitments/      - ارتباطات  
  ├── Replies/          - ردود
  ├── Projects/         - مشاريع
  └── Backup/           - نسخ احتياطية


نصائح مهمة:
===========

- ضع ملف EXE في مجلد منفصل
- لا تحذف ملف قاعدة البيانات (.db)
- اعمل نسخة احتياطية من مجلد Documents
- اختبر الملف على جهاز آخر قبل التوزيع


للدعم الفني:
============

راجع الملفات التالية للمزيد من المعلومات:
- README.md
- BUILD_EXE.md  
- INSTALLATION.md
- DEVELOPER_GUIDE.md

========================================
تم إنشاء هذا التطبيق باستخدام .NET 6 و WPF
مع دعم كامل للغة العربية
========================================
