﻿<Application x:Class="FinancialTracker.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:FinancialTracker"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Amber" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />

                <!-- Custom Colors -->
                <ResourceDictionary>
                    <Color x:Key="PrimaryColor">#2196F3</Color>
                    <Color x:Key="SecondaryColor">#FFC107</Color>
                    <Color x:Key="AccentColor">#4CAF50</Color>
                    <Color x:Key="BackgroundColor">#FAFAFA</Color>
                    <Color x:Key="SurfaceColor">#FFFFFF</Color>
                    <Color x:Key="ErrorColor">#F44336</Color>
                    <Color x:Key="WarningColor">#FF9800</Color>
                    <Color x:Key="SuccessColor">#4CAF50</Color>
                    <Color x:Key="InfoColor">#2196F3</Color>

                    <!-- Brushes -->
                    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
                    <SolidColorBrush x:Key="SecondaryBrush" Color="{StaticResource SecondaryColor}"/>
                    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>
                    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}"/>
                    <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource SurfaceColor}"/>
                    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
                    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
                    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor}"/>
                    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource InfoColor}"/>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>

            <!-- Application Fonts -->
            <FontFamily x:Key="AppFont">Segoe UI, Tahoma, Arial</FontFamily>

            <!-- Custom Styles -->
            <Style x:Key="AppTextStyle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource AppFont}"/>
                <Setter Property="FlowDirection" Value="LeftToRight"/>
                <Setter Property="TextAlignment" Value="Left"/>
            </Style>

            <Style x:Key="AppButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="FontFamily" Value="{StaticResource AppFont}"/>
                <Setter Property="FlowDirection" Value="LeftToRight"/>
            </Style>

            <Style x:Key="AppTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignTextBox}">
                <Setter Property="FontFamily" Value="{StaticResource AppFont}"/>
                <Setter Property="FlowDirection" Value="LeftToRight"/>
                <Setter Property="TextAlignment" Value="Left"/>
            </Style>

            <Style x:Key="CardStyle" TargetType="materialDesign:Card">
                <Setter Property="Margin" Value="8"/>
                <Setter Property="Padding" Value="16"/>
                <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
